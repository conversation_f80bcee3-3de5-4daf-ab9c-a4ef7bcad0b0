{"name": "brand", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build && rm -rf ../backend/dist && cp -r dist ../backend/", "lint": "vue-cli-service lint", "deploy": "npm run build && node ../frontend/deploy.js"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.4.4", "image-compressor.js": "^1.1.4", "image-webpack-loader": "^6.0.0", "postcss-loader": "^3.0.0", "ssh2-sftp-client": "^9.0.0", "vue": "^2.6.10", "vue-markdown-loader": "^2.4.1", "vue-router": "^3.1.3"}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@vitejs/plugin-legacy": "^7.2.1", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-service": "^4.1.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "jquery": "^3.5.1", "sass": "^1.90.0", "sass-loader": "^8.0.0", "vite": "^7.1.2", "vite-plugin-vue2": "^2.0.3", "vue-loader": "^15.9.3", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"no-alert": "off", "no-console": "off", "no-unused-vars": "off", "no-undef": "off", "no-mixed-spaces-and-tabs": "off"}, "parserOptions": {"parser": "@babel/eslint-parser"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}