const path = require("path");
const config = require(path.join(__dirname, "./asyncConfig"));
const Client = require("ssh2-sftp-client");
const syncFiles = () => {
  const sftp = new Client('async-dir2');
  config.username = "user_00";
  sftp
    .connect(config)
    .then(() => {
      console.log('连接成功');
      return sftp.uploadDir(
        path.join(__dirname, "../backend/dist"),
        "/web/amsbrand/backend/dist"
      );
    })
    .then((data) => {
      console.log(data, 'data');
      sftp.end();
    })
    .catch((err) => {
      console.error(err.message, 'err');
      sftp.end();
    });
};
syncFiles();
