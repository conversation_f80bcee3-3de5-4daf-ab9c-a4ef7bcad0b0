const path = require('path')
const webpack = require('webpack')
resolve = (dir) => path.join(__dirname, dir)

module.exports = {
    indexPath: "index.html",
    publicPath: "./",
    assetsDir: 'static',
    filenameHashing: true,
    lintOnSave: true,
    runtimeCompiler: false,
    transpileDependencies: [ /* string or regex */ ],
    productionSourceMap: false,
    integrity: false,
    configureWebpack: (config) => {
        const plugins = []
        config.resolve = {
            extensions: ['.js', '.vue', '.json',".css"],
            alias: {
                'vue$': 'vue/dist/vue.esm.js',
                '@': path.resolve(__dirname, './src')
            },
        }
        plugins.push(
            new webpack.ProvidePlugin({
                $: 'jquery',
                jQuery: 'jquery',
                'window.jQuery': 'jquery'
            })
        )
        config.plugins = [...config.plugins, ...plugins]
        // config.externals = {
        //     vue: "Vue",
        //     "vue-router": "VueRouter"
        // }
    },
    chainWebpack: (config) => {
        config.plugins.delete('prefetch').delete('preload')
        config.resolve.symlinks(true)
        config.module
            .rule('images')
            .use('image-webpack-loader')
            .loader('image-webpack-loader')
            .options({
                bypassOnDebug: true
            })
            .end()
        config.module
            .rule('pdf')
            .test(/\.pdf$/)
            .use('file-loader')
            .loader('file-loader')
            .options({
                name: '[name].[ext]'
            })
            .end()
        config.module
            .rule('zip')
            .test(/\.zip$/)
            .use('file-loader')
            .loader('file-loader')
            .options({
                name: '[name].[ext]'
            })
            .end()
        config.module
            .rule('md')
            .test(/\.md$/)
            .use('vue-loader')
            .loader('vue-loader')
            .end()
            .use('vue-markdown-loader')
            .loader('vue-markdown-loader/lib/markdown-compiler')
            .options({
                raw: true
            })
        // const cdn = {
        //     js: [
        //         "//unpkg.com/vue@2.6.10/dist/vue.min.js",
        //         "//unpkg.com/vue-router@3.1.3/dist/vue-router.min.js"
        //     ]
        // }
        // config.plugin("html").tap(args => {
        //     args[0].cdn = cdn
        //     return args
        // })
    },
    devServer: {
        host: 'localhost',
        port: 8080,
        https: false,
        hotOnly: false,
        before: app => {}
    },


    css: {
        extract: true,
        sourceMap: false,
        loaderOptions: {
            sass: {
                prependData: `@import"@/styles/app.scss";`,
            }
        },
        requireModuleExtension: false
    },

    parallel: require('os').cpus().length > 1,
}
