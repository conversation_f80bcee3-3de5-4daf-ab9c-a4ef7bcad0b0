import axios from "axios";

const service = axios.create({
  baseURL: window.location.origin,
  withCredentials: false,
  // timeout: 10000 // request timeout
});

/**
 * 转换为FormData格式
 * @param data
 * @returns {string}
 */
export function toFormData(data) {
  let ret = "";
  let it = "";
  for (it in data) {
    ret += encodeURIComponent(it) + "=" + encodeURIComponent(data[it]) + "&";
  }
  return ret.substring(0, ret.length - 1);
}

// request interceptor
service.interceptors.request.use(
  (config) => {
    config.headers["X-Requested-With"] = "XMLHttpRequest";
    config.headers["Content-Type"] = "application/json";
    return config;
  },
  (error) => {
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  (response) => {
    // 返回错误
    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);


export default service;
