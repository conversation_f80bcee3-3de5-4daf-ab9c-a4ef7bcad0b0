<script>

  export default {
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        bannerMsg: {
          title: '品牌介绍',
          content: [
            '所有关于腾讯广告的品牌介绍，',
            '请遵循此规范以建立专业统一的品牌形象。',
          ],
          pic: require("../assert/Banner/BrandIntro.png")
        },
        cardInfo: [
          {
            pic: require("../assets/ad_logo.png"),
            options: {
              title: '腾讯广告业务介绍',
              button: { value: '打开PDF', link: 'http://amsbrand.oa.com/files/Intro/Tencent_Marketing_Solution_Business_introduction_201907.pdf' }
            },
            ppt: {
               value: '下载PPT',
               link: 'http://amsbrand.oa.com/files/Intro/Tencent_Marketing_Solution_Business_introduction_201907.zip'
            }
          }
        ]
      }
    }
  }
</script>

<template>
  <div class="main">

    <div v-if="isBlack">
      <TsaHeaderBlack ref="header" title='品牌资源' index='1' :blueBlock="blueBlock"></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader ref="header" title='品牌资源' index='1' :blueBlock="blueBlock"></TsaHeader>
    </div>

    <TsaTitle :title="bannerMsg.title" :content="bannerMsg.content" :pic="bannerMsg.pic"></TsaTitle>

    <div class="small-card-body">
      <div class="card-container" v-for="(item, index) in cardInfo" :key="index">
        <TsaSmallCard :pic="item.pic" :options="item.options" :ppt="item.ppt" ></TsaSmallCard>
      </div>
    </div>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>

    <!-- 回到顶部板块 -->
    <GoTop v-if="goToFixed"></GoTop>
  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
  .small-card-body {
    margin: 0 auto;
    margin-top: 64px;
    max-width: 1200px;
    .card-container {
      display: inline-block;
      width: 380px;
      margin: 0 30px 0 0;
      overflow: hidden;
      background-image: url('../assert/Public/pr_nbgc.png');
      background-size: 382px auto;
      background-repeat: no-repeat;
      background-origin: border-box;
      border: 2px solid #E0EAFF;
      box-shadow: 0 4px 20px 0 rgba(0,116,255,0.08);
      border-radius: 12px;
      transition: all .2s;

      &:hover {
        border: 2px solid #296BEF;
        box-shadow: 0 1px 28px 0 rgba(44,114,255,0.1),
        0 4px 20px 0 rgba(0,116,255,0.08);
      }

      &:nth-child(n + 4) {
        margin-top: 30px;
      }
      &:nth-child(3n) {
        margin-right: 0;
      }
    }

    @media (max-width: $screen-lg) {
      max-width: 984px;

      .card-container {
          margin: 0 24px 0 0;
          width: 312px;
          background-size: 314px auto;

          &:nth-child(n + 4) {
            margin-top: 24px;
          }
        }
			}
  }
</style>
