<script>

    export default {
        data() {
            return {
                blueBlock: false,
                isBlack: false,
                info: [
                    {
                        title: '基础网格',
                        content: [
                            'AMS图标都是按照 48x48px 的画板进行设计的，制作时确保元素 与像素上的对齐。为了防止图标在运用中被裁掉边缘的可能性，四周各留出4px， 也让设计师把握图标间平衡留下了进退的余地。',
                        ],
                        img: [
                            require("../assert/VI/icon/VI_icon_01.png"),
                        ],
                    },
                    {
                        title: '布局栅格',
                        content: [
                            '为保持一致的比例，定义横向矩形、竖向矩形、正方形、圆形尺寸在栅格中的位置。图标造型尽量由圆、方、三角基础图形组成。',
                        ],
                        img: [
                            require("../assert/VI/icon/VI_icon_02.png"),
                            require("../assert/VI/icon/VI_icon_03.png"),
                        ],
                    },
                    {
                        title: '造型形式',
                        content: [
                            '延续腾讯广告品牌特征，线段末端统一圆角，半闭合样式，结合处断口，表意上结合腾讯广告原子图形。于基础网格内，外圆角为 3px，内圆角为 0.5px，线段以 3px 粗细大小为基础，根据不同的场景进行2倍进行调整。',
                        ],
                        img: [
                            require("../assert/VI/icon/VI_icon_04.png"),
                            require("../assert/VI/icon/VI_icon_05.png"),
                        ],
                    },
                    {
                        title: '组合形式',
                        content: [
                            '处理图标带斜角造型时统一用右上52度跟标志保持一致。表达多元素时，辅助元素始终在图标右下方，元素可采用原子图形。针对不通场景使用不同的表现形式。',
                        ],
                        img: [
                            require("../assert/VI/icon/VI_icon_06.png"),
                            require("../assert/VI/icon/VI_icon_07.png"),
                        ]
                    },
                ],
            }
        }
    }
</script>

<template>
    <div class="info-body">
        <TsaInfo :info="info"></TsaInfo>
    </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
    @import "../styles/tsa";
</style>
