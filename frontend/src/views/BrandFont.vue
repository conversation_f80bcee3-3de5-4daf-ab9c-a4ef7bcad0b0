<script>

  export default {
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '品牌字体',
          content: '所有关于腾讯广告设计输出物的字体运用，都请遵循此规范以保持品牌形象的识别度与统一。'
        },
        info: [
          {
            title: '中文字体',
            content: [
              '品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。',
              '方正悠黑作为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。',
              '实际使用时需要遵循下方所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
            ],
            img: [
              require("../assert/VI/font/<EMAIL>"),
              require("../assert/VI/font/<EMAIL>"),
              require("../assert/VI/font/<EMAIL>"),
            ],
          },
          {
            title: '英文字体',
            content: [
              '品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。',
              'Helvetica Neue 作为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。',
              '实际使用时需要遵循右侧所述的品牌字体运用示例，以确保品牌视觉传达的一致性。',
            ],
            img: [
              require("../assert/VI/font/<EMAIL>"),
              require("../assert/VI/font/<EMAIL>"),
              require("../assert/VI/font/<EMAIL>"),
            ],
          },
          {
            title: '腾讯体',
            content: [
              '腾讯体共含两款磅值，分别为腾讯体(TencentSans-W3) 和腾讯体(TencentSans-W7)。',
              '两者可同时使用，腾讯体(W7) 仅可起强调、突出作用，不推荐大面积使用。腾讯体(W3) 在设计中作为标题字使用的情况下，内文字体优先采用方正悠黑，其次考虑腾讯体(W3)。',
              '详细使用说明请查阅腾讯体的使用规范手册。',
            ],
            img: [
              require("../assert/VI/font/<EMAIL>"),
              require("../assert/VI/font/<EMAIL>"),
            ],
          },
        ],
      }
    }
  }
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <TsaInfo :info="info"></TsaInfo>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav
     title-left="品牌色彩" link-left="/Brand/BrandColor"
     title-right="图像" link-right="/Brand/BrandImg"
    ></TsaInfoNav> -->

  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
