<script>

  export default {
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '图像',
          content: '所有腾讯广告对外输出物中图片和影像的运用，都请遵循此规范以保持品牌形象的识别度与统一。'
        },
        info: [
          {
            title: '图像关键词',
            content: [
              '腾讯广告的品牌图像关键词为商业、人、数据技术、连接。通过具有固定风格与内容的图像来传递品牌核心信息，能增强品牌在用户心中的视觉形象。使用具有商务场景及数据图标的元素来传达助力增长的商业气质；使用多人欢乐的沟通场景来寓意广告的精确触达；使用带有编程、代码元素的场景来表现品牌的技术支撑；通过带有连接、传达含义的图像来表现广告传达的品牌本质。'
            ],
            img: [
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
            ],
          },
          {
            title: '图像风格建议',
            content: [
              '在品牌图像风格上，应紧扣交流沟通、商务合作、数据技术、指标增长、城市航拍、智能抽象等风格关键词进行应用。在选择品牌图像时，应注意图像内容与设计内容的契合度，避免盲目追求视觉效果而使用内容契合度不高的图像。如需要表现品牌广阔覆盖力与影响力时，可选择使用与内容相符的城市航拍影像。',
            ],
            img: [
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
            ],
          },
          {
            title: '图像使用禁止事项',
            content: [
              '为了保证品牌视觉形象的正向传达，在影像的拍摄与选择上需要保证内容、光线、造型、场景等所有元素都是正确。下面列举几个禁止对腾讯广告标志执行的操作。',
            ],
            img: [
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
              require("../assert/VI/img/<EMAIL>"),
            ],
          },
        ],
      }
    }
  }
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <TsaInfo :info="info"></TsaInfo>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav
     title-left="品牌字体" link-left="/Brand/BrandFont"
     title-right="二级品牌标志" link-right="/Brand/BrandSub"
    ></TsaInfoNav> -->

  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
