<script>

	export default {
		data() {
			return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: 'PPT模板',
          content: '腾讯广告PPT模板为腾讯广告演讲PPT通用模板，建议腾讯广告对内或对外的PPT展示统一使用该模板。使用前请先安装腾讯体（已附加在下载文件夹中），避免字体缺失。'
        },
				info: [
					{
						title: '中文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。思源黑体做为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_01.png"),
						noboard: true
					},
					{
						title: '英文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。Daxline Offc做为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_02.png"),
						noboard: false
					}
				],
				downloadOptions: [
					{
						name: 'PPT模板',
						pic: require("../assert/Down/ppt/<EMAIL>"),
						link: 'https://down.qq.com/tad/AMS_PPT_Template.zip',
					},
				],
			}
		}
	}
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <!-- 下载板块 -->
    <!-- 如果不是微信展示下方 -->
    <div class="down-body" v-if="!isWeixin">
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.link" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 如果是微信展示下方 -->
    <div class="down-body" v-if="isWeixin">
      <p class="down-title">下载主品牌标志</p>
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.wxlink" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <!-- 	<TsaInfoNav
     title-left="品牌标准字" link-left="/Download/DownFont"
     title-right="图片素材" link-right="/Download/DownImg"
    ></TsaInfoNav> -->

  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
