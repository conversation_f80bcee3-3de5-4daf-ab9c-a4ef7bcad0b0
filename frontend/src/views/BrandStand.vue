<script>

  export default {
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '主品牌标志使用规范',
          content: '所有关于腾讯广告主品牌标志的使用，都请遵循此规范以保持品牌形象的识别度与统一。'
        },
        info: [
          {
            title: '安全间距',
            content: [
              '在任何时候，腾讯广告标志的周围都要留有一定的安全间距，该间距范围内不得出现任何图片和文字。您可以使用字标的高度来确定标志周围的安全间距下限。',
              '如标志下方还显示文字，则标志底部与下方文字之间的安全间距应等同于该文字的X字高。'
            ],
            img: [
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
            ],
          },
          {
            title: '大小下限',
            content: [
              '在任何时候，腾讯广告的标志都应该易读易认，应遵守标志的最小使用尺寸规范。在印刷内容中，腾讯广告的横版中文标志为 5mm 高，竖版中文标志为 16mm 高。横版英文标志为 6mm 高，竖版英文标志为 12mm 高。横版中英文标志为 8mm 高，竖版中英文标志为 18mm 高。',
              '在数字版内容中，腾讯广告的横版中文标志为 20px 高，竖版中文标志为 40px 高。横版英文标志为 24px 高，竖版英文标志为 48px 高。横版中英文标志为 36px 高，竖版中英文标志为 80px 高。'
            ],
            img: [
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
            ],
          },
          {
            title: '标志背景色',
            content: [
              '为保证品牌统一的视觉体验，优先建议全彩标志，无法满足时再考虑单色标志。',
              '在 0-20% 灰度阶的背景上应使用浅色底全彩标志，在 20%-70% 灰度阶的背景上不建议使用全彩标志，在 70% -100% 灰度阶的背景应该使用深色底全彩标志。',
              '单色场景下，在 0-80% 灰度阶的背景上采用单色黑标志，在 30%-100% 灰度阶的背景应使用反白标志。在对比过强时可降低标志透明度。',
              '无论标志应用与何种背景，都应保证标志颜色和轮廓清晰可辨，易于识别。'
            ],
            img: [
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
            ],
          },
          {
            title: '标志使用的禁止事项',
            content: [
              '为保证品牌标志在传播与识别的完整性及统一性，请勿擅自更改标志。下面列举几个禁止对腾讯广告标志执行的操作。',
            ],
            img: [
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
              require("../assert/VI/stand/<EMAIL>"),
            ],
          },
          {
            title: '联合品牌组合规范',
            content: [
              '当腾讯广告与合作商标一同使用时，请遵循此规范进行使用，保持品牌形象的统一性和稳定性，不得擅自变更为其它形式。为使标志与物料内容相对独立清晰，建议放置于物料的四角位置，并遵循网格与不可侵范围。',
            ],
            img: [
              require("../assert/VI/stand/<EMAIL>"),
            ],
          },
        ],
      }
    }
  }
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <TsaInfo :info="info"></TsaInfo>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav
     title-left="主品牌标志" link-left="/Brandnoc"
     title-right="品牌色彩" link-right="/Brand/BrandColor"
    ></TsaInfoNav> -->

  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
