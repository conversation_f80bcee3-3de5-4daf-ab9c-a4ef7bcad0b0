<template>
  <div class="info-body">
    <div class="intro-title">腾讯广告</div>

    <div class="intro-content">
      腾讯⼴告作为腾讯⾯向企业统⼀的商业服务平台，致⼒成为“企业全域经营伙伴”。我们依托“全场景⽤户连接”、“全链路经营提效⽀撑”、“⾏业化营销服务”，以及“服务⽣态体系”四⼤核⼼能⼒，帮助⼴告主实现数字化经营与⽣意增长。
    </div>
    <div class="intro-content">
      A uniform business service platform, TMS positions itself as an
      “Omni-channel Marketing Partner of Enterprises”. Taking advantage of our
      four major capabilities, including “full-scenario connection to users”,
      “business efficiency improvement across the entire consumer
      journey”,“industry-specific marketing service” and“service ecosystem”, we
      help advertisers in their digital operations and business growth.
    </div>

    <div class="intro-video">
      <video controls="controls">
        <source src="../assets/intro.mp4" type="video/mp4" />
      </video>
    </div>

    <div class="intro-bottom">
      <a
        :href="item.link"
        v-for="(item, key) in list"
        class="down-button"
        :key="key"
      >
        <i class="down-icon"></i>
        {{ item.title }}
      </a>
      <!-- http://amsbrand.oa.com/files/Brand_ppt/About_Tencent_Marketing_Solution_Chinese_edition.pptx.zip -->
      <!--      <a :href="ChinesePPT" class="down-button">-->
      <!--        <i class="down-icon"></i>-->
      <!--        腾讯广告业务中文版介绍PPT-->
      <!--      </a>-->

      <!-- http://amsbrand.oa.com/files/Brand_ppt/About_Tencent_Marketing_Solution_English_edition.pptx.zip -->
      <!--      <a href="http://amsbrand.oa.com/files/Brand_ppt/About_Tencent_Marketing_Solution_English_edition.ppt.zip" class="down-button">-->
      <!--        <i class="down-icon"></i>-->
      <!--        腾讯广告业务英文版介绍PPT-->
      <!--      </a>-->
    </div>
    <!-- 底部导航 -->
    <!--<TsaInfoNav
    title-right="主品牌标志使用规范" link-right="/Brand/BrandStand"
   ></TsaInfoNav> -->
  </div>
</template>

<script>
// import ChinesePPT from '@/files/Brand_ppt/About_Tencent_Marketing_Solution_Chinese_edition.ppt.zip'
export default {
  data() {
    return {
      blueBlock: false,
      isBlack: false,
      list: [
        {
          // link: 'https://down.qq.com/tad/About_Tencent_Marketing_Solution_Chinese_edition.ppt.zip',
          link: "https://down.qq.com/tad/2022AMS_Credentials-Chinese-2.pptx.zip",
          title: "腾讯广告业务中文版介绍PPT",
        },
        {
          // link: 'https://down.qq.com/tad/About_Tencent_Marketing_Solution_English_edition.ppt.zip',
          link: "https://down.qq.com/tad/2022AMS_Credentials-English.pptx.zip",
          title: "腾讯广告业务英文版介绍PPT",
        },
      ],
    };
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";

.intro-title {
  font-size: 36px;
  font-family: PingFangSC-Medium;
  margin-bottom: 20px;
  color: #0b1531;

  @media (max-width: $screen-lg) {
    font-size: 32px;
  }
}
.intro-content {
  margin-bottom: 12px;
  line-height: 1.8;
  font-size: 16px;
  color: #0b1531;
  letter-spacing: 0.5px;

  @media (max-width: $screen-lg) {
    font-size: 14px;
  }
}

.intro-video {
  margin-top: 30px;

  video {
    width: 100%;
    outline: none;
  }
}

.intro-bottom {
  margin-top: 40px;
  padding-top: 40px;
  display: flex;
  justify-content: space-between;
  border-top: 2px solid #e8efff;
}
.down-icon {
  position: relative;
  top: 2px;
  left: 4px;
  margin-right: 10px;
  display: inline-block;
  width: 18px;
  height: 18px;
  transition: all 0.8s;
  background-image: url("../assert/ICON/icon_branddown.svg");
  background-position: center;
  z-index: 1;
}
.down-button {
  display: inline-block;
  width: 340px;
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  border: 2px solid #e8efff;
  border-radius: 8px;
  text-align: center;
  color: #8f9fcc;
  transition: all 0.8s;

  &:hover {
    color: #296bef;
    border-color: #296bef;
    box-shadow: 0 4px 20px rgba(0, 116, 255, 0.15);
    font-weight: bold;
    .down-icon {
      background-image: url("../assert/ICON/icon_branddown_hover.svg");
    }
  }

  @media (max-width: $screen-lg) {
    width: 308px;
    height: 48px;
    line-height: 48px;
    font-size: 14px;
  }
}
</style>
