<script>

    export default {
        data() {
            return {
                blueBlock: false,
                isBlack: false,
                infoHead: {
                    title: '插画运用原则',
                    content: '腾讯广告插画适用于官网、公众号、小程序、产品手 册等涉及信息解读、概念诠释的内容，包含线上线下 等使用场景。其应遵循以下原则:'
                },
                info: [
                    {
                        title: '插画运用原则',
                        content: [
                            '腾讯广告插画适用于官网、公众号、小程序、产品手 册等涉及信息解读、概念诠释的内容，包含线上线下 等使用场景。其应遵循以下原则:',
                            '可识别性:专业地辅助理解信息，减少干扰。',
                            '品牌个性:强调品牌形象，为内容注入品牌基因。',
                            '一致性:深化感官认知，强化腾讯广告品牌形象在受众的心智形象。',
                        ],
                        img: [
                            require("../assert/VI/illustration/VI_illustration_01.png"),
                            require("../assert/VI/illustration/VI_illustration_02.png"),
                        ],
                    },
                    {
                        title: '插画的组织设计',
                        content: [
                            '插画的视觉呈现需要彰显更多的品牌个性。提炼品牌 LOGO的原子图形组合品牌形象，弱化图形棱角，圆润的图形体现品牌的亲和力;避免细致的人物刻画; 保持留白的想象空间。',
                            '人物形象应建立在平均风格上，避免过度的抽象和夸张。采取稳定的人物结构比例，能加深受众对品牌插画人物的识别度。',
                            '采用平行透视的画面视角，避免三维立体的角度，更符合扁平化的特征性质，能灵活地切换素材服用，便于不同组件组合。',
                        ],
                        img: [
                            require("../assert/VI/illustration/VI_illustration_03.png"),
                            require("../assert/VI/illustration/VI_illustration_04.png"),
                        ],
                    },
                    {
                        title: '插画的配色原则',
                        content: [
                            '根据腾讯广告的色彩基础色板，加深受众对腾讯广告的色彩认知，采用黄金分割的色彩比例配色，根据使用场景的丰富程度可以纳入其他辅助色，确保 1-2 个主色及 1-5 个辅助色的视觉平衡。',
                        ],
                        img: [
                            require("../assert/VI/illustration/VI_illustration_05.png"),
                            require("../assert/VI/illustration/VI_illustration_06.png"),
                            require("../assert/VI/illustration/VI_illustration_07.png"),
                            require("../assert/VI/illustration/VI_illustration_08.png"),
                            require("../assert/VI/illustration/VI_illustration_09.png"),
                            require("../assert/VI/illustration/VI_illustration_10.png"),
                            require("../assert/VI/illustration/VI_illustration_11.png"),
                            require("../assert/VI/illustration/VI_illustration_12.png"),
                        ],
                    },
                    {
                        title: '插画的类型',
                        content: [
                            '相较于抽象的品牌概念，实际的使用场景能更加具象。 插画的目的是解释内容文本，它绝不能分散或掩盖关键 信息。目前针对腾讯广告官网网站中的运用将插图进行 几个复杂程度的划分。',
                            '场景插画:旨在讲述复杂的故事，意味需要着更大的呈现空间，可以隐喻地陈述内容。',
                            '线性插画:在视觉上和内容陈述可以是略微简化的场景插图,只关注核心的主题部分。',
                            '点性插画:图形陈述上相对简单,所占篇幅也比较小,内容间距离较近，一般是高度概括内容呈现信息。',
                        ],
                        img: [
                            require("../assert/VI/illustration/VI_illustration_13.png"),
                            require("../assert/VI/illustration/VI_illustration_14.png"),
                            require("../assert/VI/illustration/VI_illustration_15.png"),
                        ]
                    },
                ],
            }
        }
    }
</script>

<template>
    <div class="info-body">
<!--        <TsaInfoHead :infohead="infoHead"></TsaInfoHead>-->
        <TsaInfo :info="info"></TsaInfo>
    </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
    @import "../styles/tsa";
</style>
