<script>

	export default {
		data() {
			return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '品牌标志',
          content: '标志元素间的大小及位置是固定的，标志只能从规范文件中拷贝使用，不能重新绘制或擅自组合。'
        },
				info: [
					{
						title: '中文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。思源黑体做为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_01.png"),
						noboard: true
					},
					{
						title: '英文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。Daxline Offc做为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_02.png"),
						noboard: false
					}
				],
				downloadOptions: [
					{
						name: '腾讯广告标志',
						pic: require("../assert/Down/sign/<EMAIL>"),
						link: 'https://down.qq.com/tad/AMS_Logo.zip',
					},
					{
						name: '二级品牌组合规范',
						pic: require("../assert/Down/sign/<EMAIL>"),
						link: 'https://down.qq.com/tad/AMS_Sub_Brand.zip',
					},
					{
						name: '微信广告标志',
						pic: require("../assert/Down/sign/<EMAIL>"),
						link: 'https://down.qq.com/tad/Wechat_Ads_Sign.zip',
					},
					{
						name: 'QQ广告标志',
						pic: require("../assert/Down/sign/<EMAIL>"),
						link: 'https://down.qq.com/tad/QQ_Ads_Sign.zip',
					},
					{
						name: '腾讯视频组合标志',
						pic: require("../assert/Down/sign/Down_sign_05.png"),
						link: 'https://down.qq.com/tad/Video_Ads_Sign.zip',
					},
					{
						name: '腾讯新闻组合标志',
						pic: require("../assert/Down/sign/Down_sign_06.png"),
						link: 'https://down.qq.com/tad/News_Ads_Sign.zip',
					},
					// {
					// 	name: '腾讯看点组合标志',
					// 	pic: require("../assert/Down/sign/Down_sign_07_01.png"),
					// 	link: 'https://down.qq.com/tad/Kandian_Ads_Sign.zip',
					// },
					{
						name: '优量广告标志',
						pic: require("../assert/Down/sign/<EMAIL>"),
						link: 'https://down.qq.com/tad/Adnetwork_Ads_Sign.zip',
					},
					{
						name: '优量汇标志',
						pic: require("../assert/Down/sign/<EMAIL>"),
						link: 'https://down.qq.com/tad/Adnetwork_Sign.zip',
					},
					// {
					// 	name: '腾讯音乐广告标志',
					// 	pic: require("../assert/Down/sign/<EMAIL>"),
					// 	link: 'https://down.qq.com/tad/Music_Ads_Sign.zip',
					// },
					{
						name: '腾讯智慧营销品牌标识',
						pic: require("../assert/Down/sign/<EMAIL>"),
						link: 'https://down.qq.com/tad/Tencent_IN_Sign.zip',
					},
				],
			}
		}
	}
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <!-- 下载板块 -->
    <!-- 如果不是微信展示下方 -->
    <div class="down-body" v-if="!isWeixin">
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.link" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 如果是微信展示下方 -->
    <div class="down-body" v-if="isWeixin">
      <p class="down-title">下载主品牌标志</p>
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.wxlink" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav 				 title-left="品牌视觉规范" link-left="/Download/DownStandard" title-right="品牌标准字" link-right="/Download/DownFont"></TsaInfoNav>
-->
  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
