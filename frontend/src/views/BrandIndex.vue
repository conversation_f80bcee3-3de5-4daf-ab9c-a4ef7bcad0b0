<script>
import Brand2x from "@/assert/Banner/<EMAIL>";
export default {
  created() {
    this.watchScroll();
  },
  beforeDestroy() {
    this.unWatchScroll();
  },
  data() {
    return {
      blueBlock: false,
      isBlack: false,
      bannerMsg: {
        title: "品牌形象规范",
        content: [
          "所有关于腾讯广告的主品牌与二级品牌的应用，",
          "请遵循此规范以建立专业统一的品牌形象。",
        ],
        pic: Brand2x,
      },
      sidebarOptions: [
        {
          name: "品牌介绍",
          link: "/Brand",
          index: 0,
        },
        {
          name: "主品牌标志",
          link: "/Brand/Brandnoc",
          index: 1,
        },
        {
          name: "主品牌标志使用规范",
          link: "/Brand/BrandStand",
          index: 2,
        },
        {
          name: "品牌色彩",
          link: "/Brand/BrandColor",
          index: 3,
        },
        {
          name: "品牌字体",
          link: "/Brand/BrandFont",
          index: 4,
        },
        {
          name: "图像",
          link: "/Brand/BrandImg",
          index: 5,
        },
        {
          name: "品牌插画",
          link: "/Brand/BrandIllustration",
          index: 6,
        },
        {
          name: "图标",
          link: "/Brand/BrandIcon",
          index: 7,
        },
        {
          name: "二级品牌标志",
          link: "/Brand/BrandSub",
          index: 8,
        },
        {
          name: "Tencent in 使用规范",
          link: "/Brand/BrandTencentIn",
          index: 9,
        },
      ],
      isFixed: false,
    };
  },
  methods: {
    changeRoute() {
      document.documentElement.scrollTop = 380;
    },
  },
};
</script>

<template>
  <div class="main">
    <div v-if="isBlack">
      <TsaHeaderBlack
        ref="header"
        title="品牌资源"
        index="0"
        :blueBlock="blueBlock"
      ></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader
        ref="header"
        title="品牌资源"
        index="0"
        :blueBlock="blueBlock"
      ></TsaHeader>
    </div>

    <TsaTitle
      ref="titleBlock"
      id="title"
      :title="bannerMsg.title"
      :content="bannerMsg.content"
      :pic="bannerMsg.pic"
    ></TsaTitle>

    <div class="main-body">
      <TsaSidebar :options="sidebarOptions" @change="changeRoute"></TsaSidebar>
      <div class="center-body">
        <!-- 信息板块 -->
        <router-view />
      </div>

      <!-- 回到顶部板块 -->
      <GoTop v-if="goToFixed"></GoTop>
    </div>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";

.intro-title {
  font-size: 36px;
  font-family: PingFangSC-Medium;
  margin-bottom: 20px;
  color: #0b1531;

  @media (max-width: $screen-lg) {
    font-size: 32px;
  }
}
.intro-content {
  margin-bottom: 12px;
  line-height: 1.8;
  font-size: 16px;
  color: #0b1531;
  letter-spacing: 0.5px;

  @media (max-width: $screen-lg) {
    font-size: 14px;
  }
}

.intro-video {
  margin-top: 30px;

  video {
    width: 100%;
    outline: none;
  }
}

.intro-bottom {
  margin-top: 40px;
  padding-top: 40px;
  display: flex;
  justify-content: space-between;
  border-top: 2px solid #e8efff;
}
.down-icon {
  position: relative;
  top: 2px;
  left: 4px;
  margin-right: 10px;
  display: inline-block;
  width: 18px;
  height: 18px;
  transition: all 0.8s;
  background-image: url("../assert/ICON/icon_branddown.svg");
  background-position: center;
  z-index: 1;
}
.down-button {
  display: inline-block;
  width: 340px;
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  border: 2px solid #e8efff;
  border-radius: 8px;
  text-align: center;
  color: #8f9fcc;
  transition: all 0.8s;

  &:hover {
    color: #296bef;
    border-color: #296bef;
    box-shadow: 0 4px 20px rgba(0, 116, 255, 0.15);
    font-weight: bold;
    .down-icon {
      background-image: url("../assert/ICON/icon_branddown_hover.svg");
    }
  }

  @media (max-width: $screen-lg) {
    width: 308px;
    height: 48px;
    line-height: 48px;
    font-size: 14px;
  }
}
.fixed_margin {
  margin-left: 244px;
  @media screen and (max-width: $screen-lg) {
    margin-left: 200px;
  }
}
</style>
