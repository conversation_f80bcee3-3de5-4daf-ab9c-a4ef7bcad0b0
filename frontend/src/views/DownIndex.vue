<script>
// Import all required assets
import bannerPic from "../assert/Banner/<EMAIL>";
import fontImg01 from "../assets/font/font_01.png";
import fontImg02 from "../assets/font/font_02.png";
import signImg01 from "../assert/Down/sign/<EMAIL>";
import signImg02 from "../assert/Down/sign/<EMAIL>";
import signImg03 from "../assert/Down/sign/<EMAIL>";
import signImg04 from "../assert/Down/sign/<EMAIL>";
import signImg05 from "../assert/Down/sign/Down_sign_05.png";
import signImg06 from "../assert/Down/sign/Down_sign_06.png";
import signImg08 from "../assert/Down/sign/<EMAIL>";
import signImg09 from "../assert/Down/sign/<EMAIL>";
import signImg12 from "../assert/Down/sign/<EMAIL>";

export default {
  created() {
    this.watchScroll();
  },
  beforeDestroy() {
    this.unWatchScroll();
  },
  data() {
    return {
      blueBlock: false,
      isBlack: false,
      bannerMsg: {
        title: "资源下载",
        content: [
          "欢迎下载腾讯广告官方品牌视觉素材文档。",
          "请遵循规范以保持对外品牌传播的形象统一。",
        ],
        pic: bannerPic,
      },
      sidebarOptions: [
        {
          name: "品牌视觉规范",
          link: "/Download",
          index: 0,
        },
        {
          name: "品牌标志",
          link: "/Download/sign",
          index: 1,
        },
        {
          name: "品牌标准字",
          link: "/Download/DownFont",
          index: 2,
        },
        {
          name: "PPT 模板",
          link: "/Download/DownPPT",
          index: 3,
        },
        {
          name: "图片素材",
          link: "/Download/DownImg",
          index: 4,
        },
        {
          name: "图标素材",
          link: "/Download/DownIcon",
          index: 5,
        },
        {
          name: "海报模板",
          link: "/Download/DownPoster",
          index: 6,
        },
      ],
      infoHead: {
        title: "品牌标志",
        content:
          "标志元素间的大小及位置是固定的，标志只能从规范文件中拷贝使用，不能重新绘制或擅自组合。",
      },
      info: [
        {
          title: "中文",
          content: [
            "品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。思源黑体做为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。",
          ],
          img: fontImg01,
          noboard: true,
        },
        {
          title: "英文",
          content: [
            "品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。Daxline Offc做为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。",
          ],
          img: fontImg02,
          noboard: false,
        },
      ],
      downloadOptions: [
        {
          name: "腾讯广告标志",
          pic: signImg01,
          link: "https://down.qq.com/tad/AMS_Logo.zip",
        },
        {
          name: "二级品牌组合规范",
          pic: signImg02,
          link: "https://down.qq.com/tad/AMS_Sub_Brand.zip",
        },
        {
          name: "微信广告标志",
          pic: signImg03,
          link: "https://down.qq.com/tad/Wechat_Ads_Sign.zip",
        },
        {
          name: "QQ广告标志",
          pic: signImg04,
          link: "https://down.qq.com/tad/QQ_Ads_Sign.zip",
        },
        {
          name: "腾讯视频组合标志",
          pic: signImg05,
          link: "https://down.qq.com/tad/Video_Ads_Sign.zip",
        },
        {
          name: "腾讯新闻组合标志",
          pic: signImg06,
          link: "https://down.qq.com/tad/News_Ads_Sign.zip",
        },
        // {
        //   name: '腾讯看点组合标志',
        //   pic: require("../assert/Down/sign/Down_sign_07_01.png"),
        //   link: 'https://down.qq.com/tad/Kandian_Ads_Sign.zip',
        // },
        {
          name: "优量广告标志",
          pic: signImg08,
          link: "https://down.qq.com/tad/Adnetwork_Ads_Sign.zip",
        },
        {
          name: "优量汇标志",
          pic: signImg09,
          link: "https://down.qq.com/tad/Adnetwork_Sign.zip",
        },
        // {
        //   name: '腾讯音乐广告标志',
        //   pic: require("../assert/Down/sign/<EMAIL>"),
        //   link: 'https://down.qq.com/tad/Music_Ads_Sign.zip',
        // },
        {
          name: "腾讯智慧营销品牌标识",
          pic: signImg12,
          link: "https://down.qq.com/tad/Tencent_IN_Sign.zip",
        },
      ],
      isFixed: false,
    };
  },
  methods: {
    changeRoute() {
      document.documentElement.scrollTop = 380;
    },
  },
};
</script>

<template>
  <div class="main">
    <!-- <TsaHeader ref="header" title='品牌资源' index='3'></TsaHeader> -->
    <div v-if="isBlack">
      <TsaHeaderBlack
        ref="header"
        title="品牌资源"
        index="2"
        :blueBlock="blueBlock"
      ></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader
        ref="header"
        title="品牌资源"
        index="2"
        :blueBlock="blueBlock"
      ></TsaHeader>
    </div>

    <TsaTitle
      ref="titleBlock"
      :title="bannerMsg.title"
      :content="bannerMsg.content"
      :pic="bannerMsg.pic"
    ></TsaTitle>

    <div class="main-body">
      <TsaSidebar :options="sidebarOptions" @change="changeRoute"></TsaSidebar>

      <div class="center-body">
        <router-view />
      </div>
    </div>

    <!-- 回到顶部板块 -->
    <GoTop v-if="goToFixed"></GoTop>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";
.fixed_margin {
  margin-left: 244px;
  @media screen and (max-width: $screen-lg) {
    margin-left: 200px;
  }
}
</style>
