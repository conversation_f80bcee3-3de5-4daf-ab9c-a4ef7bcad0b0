<script>

  export default {
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        info: [
          {
            title: '品牌简介',
            content: [
              '腾讯广告是腾讯集团的核心广告业务。',
              '承载覆盖10亿受众的微信、QQ、QQ空间等领先社交平台，及QQ浏览器、应用宝、腾讯新闻、腾讯视频、天天快报以及第三方应用等丰富广告场景。帮助广告主实现精准用户触达和多样用户互动，助力品牌与商业目标的实现，构建品牌与消费者的美好连接。',
              '同时，作为腾讯集团数字商业的核心驱动力，腾讯广告通过数据和营销能力的开放，与流量方、技术合作伙伴、零售等行业、及其他第三方展开合作，以创新持续拓展营销边界，构建开放共赢的生态体系，共同推动数字营销行业发展。'
            ],
            img: require("../assets/brand/brand_01.png"),
            noboard: true
          },
          {
            title: '通用标志2',
            content: [
              '此为腾讯广告主标志，不受尺寸或制作工艺限制的情况下，统一使用标志。2',
              '一致连贯的使用品牌标志有助于保持品牌的统一性，是品牌更容易识别。',
              '使用中不得改变其形状、结构和比例。'
            ],
            img: require("../assets/test.png"),
            noboard: true
          },
          {
            title: '通用标志3',
            content: [
              '此为腾讯广告主标志，不受尺寸或制作工艺限制的情况下，统一使用标志。2',
              '一致连贯的使用品牌标志有助于保持品牌的统一性，是品牌更容易识别。',
              '使用中不得改变其形状、结构和比例。'
            ],
            img: require("../assets/test.png"),
            noboard: false
          },
        ],
        download: []
      }
    }
  }
</script>

<template>
  <div class="main">

    <div class="cartoon-block" :class="cartoon == true ? 'no-cartoon' : ''">
      <cartoon></cartoon>
    </div>

    <TsaHeader ref="header" title='品牌资源' index='0' :blueBlock="blueBlock"></TsaHeader>
    <div class="main-body">
      <!-- 标题板块 -->
      <!-- <TsaTitle title='主品牌标志' content='以下为腾讯广告标志的使用规范。一致连贯地使用品牌标志有助于保持品牌的统一性，使品牌更容易被识别。'></TsaTitle> -->

      <div ref="titleBlock" id="title" class="titile-block">
        <p class="title">主品牌标志</p>
        <div class="title-content">以下为腾讯广告标志的使用规范。一致连贯地使用品牌标志有助于保持品牌的统一性，使品牌更容易被识别。</div>
        <!-- 查看pdf板块 -->
        <div class="pdf-block">
          <a class="pdf-part" href="//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/pdf/TSA Brand Indentity Guideline_V1.2.pdf">
            <i class="icon-pdf"></i>
            <div class="pdf-content">
              <p class="pdf-title">腾讯广告品牌视觉规范
                <i class="pdf-iconright"></i>
              </p>
              <p class="pdf-version">Version 1.2 / 2018.7</p>
            </div>
          </a>
        </div>
      </div>

      <div class="center-body">
        <div class="blue-block"></div>
        <!-- 信息板块 -->
        <div class="info-body">
          <TsaInfo :info="info"></TsaInfo>
        </div>

        <!-- 下载板块 -->
        <div class="down-body">
          <p class="down-title">下载主品牌标识</p>
          <div class="down-block">
            <div class="down-child">
              <TsaDown :image='require("../assets/down.png")' :href='require("../assets/down.png")' name='腾讯广告标志'></TsaDown>
              <div class="down-content-block">
                <p>版权使用范围：</p>
                <p>1.腾讯公司所属业务与部门可使用腾讯体；</p>
                <p>1.腾讯公司所属业务与部门可使用腾讯体腾讯体；</p>
                <p>1.腾讯公司所属业务与部门可使用腾讯体；</p>
              </div>
            </div>
            <div class="down-child">
              <TsaDown :image='require("../assets/down.png")' :href='require("../assets/down.png")' name='腾讯广告标志'></TsaDown>
            </div>
            <div class="down-child">
              <TsaDown :image='require("../assets/down.png")' :href='require("../assets/down.png")' name='腾讯广告标志'></TsaDown>
            </div>
          </div>
          <p class="down-title">二级品牌标志</p>
          <div class="down-block ">
            <div class="down-child">
              <TsaDown :image='require("../assets/down.png")' :href='require("../assets/down.png")' name='腾讯广告标志'></TsaDown>
              <div class="down-content-block">
                <p>版权使用范围：</p>
                <p>1.腾讯公司所属业务与部门可使用腾讯体；</p>
                <p>1.腾讯公司所属业务与部门可使用腾讯体腾讯体；</p>
                <p>1.腾讯公司所属业务与部门可使用腾讯体；</p>
              </div>
            </div>
            <div class="down-child">
              <TsaDown :image='require("../assets/down.png")' :href='require("../assets/down.png")' name='腾讯广告标志'></TsaDown>
            </div>
            <div class="down-child">
              <TsaDown :image='require("../assets/down.png")' :href='require("../assets/down.png")' name='腾讯广告标志'></TsaDown>
            </div>
          </div>
        </div>
      </div>

      <!-- 查看pdf板块 -->
      <div class="pdf-block">
        <a class="pdf-part" href="./static/pdf/1.pdf" target="_blank">
          <div class="pdf-bgc"></div>
          <i class="icon-pdf"></i>
          <div class="pdf-content">
            <p class="pdf-title">腾讯广告品牌视觉规范
              <i class="pdf-iconright"></i>
            </p>
            <p class="pdf-version">Version 1.2 / 2018.7</p>
          </div>
        </a>
      </div>

      <!-- 上下页面板块 -->
      <!-- <div class="go-block">
        <div class="go-part">
          <a class="link" href="/ChildBrand">
            <span class="content-left">
              <i class="go-iconleft"></i>
              <span>二级品牌标志</span>
            </span>
          </a>
        </div>
        <div class="go-part">
          <a class="link" href="/ChildBrand">
            <span class="content-right">
              <span>二级品牌标志</span>
              <i class="go-iconright"></i>
            </span>
          </a>
        </div>
      </div> -->

      <!-- 回到顶部板块 -->
      <GoTop v-if="goToFixed"></GoTop>
    </div>

    <!-- 底部板块 -->
    <div class="footer">
      <p>腾讯广告 市场公关中心&设计中心</p>
      <p>Copyright © 1998 - 2019Tencent Inc. All Rights Reserved. </p>
      <img class="footer-bgc" src="../assets/footer.png" alt="">
    </div>
  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
