<template>
  <div>
    <div id="avatar-crop" class="avatar-crop">
      <div class="avatar-crop-inner">
        <div ref="imageArea" id="upload-demo"></div>
        <a
          href="javascript:void(0);"
          id="upload-avatar-result"
          class="button button-small"
          >确定</a
        >
      </div>
    </div>

    <div id="qrcode-crop" class="avatar-crop">
      <div class="avatar-crop-inner">
        <div ref="codeArea" id="upload-code"></div>
        <a
          href="javascript:void(0);"
          id="upload-qrcode-result"
          class="button button-small"
          >确定</a
        >
      </div>
    </div>

    <BoxHead v-show="createdImg">
      <div slot="title"><IconSuccess />成功生成邮件签名</div>
      <div slot="right"></div>
    </BoxHead>
    <BoxHead v-show="!createdImg">
      <div slot="title">请填写真实信息</div>
      <div slot="right">
        <router-link to="/MailDemo" class="special-link add pull-right" exact>
          查看签名范例</router-link
        >
      </div>
    </BoxHead>

    <BoxContent v-show="createdImg" height="493px" style="overflow: hidden">
      <div class="result">
        <div class="result-img">
          <img :src="createdImg" />
        </div>
        <div class="result-info">
          下载邮件签名图片，并在邮箱中设置签名
          <a :href="faqLink" target="_blank" style="margin-left: 15px"
            >签名设置</a
          >
          <a href="javascript:void(0);" class="pull-right" @click="resetForm"
            >重新制作邮件签名</a
          >
        </div>
        <div style="margin-top: 62px; text-align: center">
          <a download="邮箱签名.png" :href="createdImg" class="button"
            >下载邮件签名</a
          >
        </div>
      </div>
    </BoxContent>

    <div class="creatSign" ref="imageCreate">
      <div class="creatSign-layout">
        <img class="html2canvas_logo" src="../assets/html2canvas_logo.jpg" />
        <div class="creatSign__department-layer">
          <div class="creatSign__department">{{ imgData.departString }}</div>
        </div>
        <div class="creatSign__person-layout">
          <div class="createSign__person-info">
            <div class="creatSign__image">
              <img :src="imgData.avatarData" alt="" />
            </div>
            <div class="createSign__person-name">
              <div class="creatSign__name">{{ imgData.rtxName }}</div>
              <div class="creatSign__job">{{ imgData.occupation }}</div>
            </div>
          </div>
          <div class="createSign__contact-layout">
            <div class="createSign__contact-info">
              <div class="createSign__qrcode">
                <img :src="qrcodeData" alt="" />
                <!-- <img src="../assets/erweima.jpg" alt="" /> -->
              </div>
              <ul class="createSign__contact-ways">
                <li class="createSign__contact-li" v-if="imgData.tel">
                  <img
                    class="createSign__contact__icon_img"
                    src="../assets/tel.png"
                  />
                  <p class="createSign__contact-text">
                    {{ imgData.tel }}
                  </p>
                </li>
                <li class="createSign__contact-li">
                  <img
                    class="createSign__contact__icon_img"
                    src="../assets/phone.png"
                  />

                  <p class="createSign__contact-text">{{ imgData.mobile }}</p>
                </li>
                <li class="createSign__contact-li">
                  <img
                    class="createSign__contact__icon_img"
                    src="../assets/location.png"
                  />
                  <p class="createSign__contact-text">
                    {{ imgData.locationString }}
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <BoxContent v-show="!createdImg" height="493px" verticalCenter="true">
      <form class="form" style="">
        <div class="flex avatar">
          <div style="text-align: center">
            <div class="uploader__avatar button-hover-trigger">
              <div class="uploader-preview">
                <VerticalCenter>
                  <img :src="avatarData" alt="" />
                </VerticalCenter>
              </div>
              <a
                href="javascript:void(0);"
                class="button button-small"
                :class="{ 'button-gray': avatarSuccess }"
                >{{ !avatarSuccess ? "上传真实头像" : "重新上传头像" }}</a
              >
              <input type="file" id="avatar-upload" value="选择图片文件" />
            </div>
            <div v-if="avatarSuccess" class="upload-success">
              <IconSuccess />头像上传成功！
            </div>
            <ul v-else class="upload-desc">
              <li>请上传一张真人靓照</li>
              <li>五官清晰，高清画质</li>
            </ul>
          </div>
        </div>

        <div class="flex qrcode" v-if="notShowQrcodeUploader">
          <div style="text-align: center">
            <div class="uploader__avatar button-hover-trigger">
              <div class="uploader-preview">
                <VerticalCenter>
                  <!-- <div ref='qrcodeGenerate'></div> -->
                  <img :src="qrcodeData" alt="" />
                </VerticalCenter>
              </div>
              <a
                href="javascript:void(0);"
                class="button button-small"
                :class="{ 'button-gray': qrcodeSuccess }"
                >{{ !qrcodeSuccess ? "上传微信二维码" : "重新上传二维码" }}</a
              >
              <input type="file" id="qrcode-upload" value="选择图片文件" />
            </div>
            <div v-if="qrcodeSuccess" class="upload-success">
              <IconSuccess />二维码上传成功！
            </div>
            <ul v-else class="upload-desc">
              <li>请从微信端保存二维码图片上传</li>
              <li>
                请使用常规黑白二维码图案，以防无法读取二维码信息
                <div class="tips-block">
                  <i class="tips"></i>
                  <div class="tips-img">
                    <div class="tips-mainblock"><img :src="tipImg" /></div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div class="flex others">
          <VerticalCenter>
            <div class="form__group">
              <label class="form__label">
                <span class="label__text">RTX</span>
                <input
                  placeholder="如：Threezhang 张三"
                  tabindex="1"
                  id="rtxName"
                  type="text"
                  class="input input_capitalize"
                  v-model="form.rtxName"
                />
              </label>
            </div>

            <div class="form__group">
              <label class="form__label">
                <span class="label__text">职位</span>
                <input
                  placeholder="如：视觉设计师"
                  tabindex="2"
                  id="occupation"
                  type="text"
                  class="input"
                  v-model="form.occupation"
                />
              </label>
            </div>

            <div class="form__group" v-if="showDepartSelect">
              <label class="form__label">
                <span class="label__text">部门</span>
                <select
                  tabindex="3"
                  class="select-dep"
                  id="department"
                  v-model="form.depart"
                >
                  <option value="" disabled>请选择部门</option>
                  <option
                    v-for="(val, k) in departs"
                    :value="typeof val === 'object' ? val.value : k"
                    :key="k"
                  >
                    {{ typeof val === "object" ? val.label : val }}
                  </option>
                </select>
              </label>
            </div>

            <div class="form__group" v-if="form.depart === '_customize'">
              <label class="form__label">
                <span class="label__text"></span>
                <input
                  placeholder="自定义部门名称"
                  tabindex="2"
                  id="occupation"
                  type="text"
                  class="input"
                  v-model="form.customizeDepart"
                />
              </label>
            </div>

            <div class="form__group">
              <label class="form__label">
                <span class="label__text">地址</span>
                <select
                  style="width: 159px"
                  tabindex="6"
                  class="select"
                  id="location"
                  @change="changeLoc"
                  v-model="form.location"
                >
                  <option
                    v-for="(val, k) in locations"
                    :value="typeof val === 'object' ? val.value : k"
                    :key="k"
                  >
                    {{ typeof val === "object" ? val.label : val }}
                  </option>
                </select>
              </label>
            </div>

            <div class="form__group" v-if="form.location === '_customize'">
              <label class="form__label">
                <span class="label__text"></span>
                <input
                  placeholder="自定义地址"
                  tabindex="2"
                  id="occupation"
                  type="text"
                  class="input"
                  v-model="form.customizeLocation"
                />
              </label>
            </div>

            <div class="form__group">
              <label class="form__label">
                <span class="label__text">座机</span>
                <span style="color: #a6a6a6">{{ form.pretel }}</span>
                <input
                  tabindex="4"
                  id="tel"
                  v-model="form.tel"
                  class="input"
                  onkeyup="this.value=this.value.replace(/\D/g,'')"
                  maxlength="6"
                  style="width: 100px"
                />
              </label>
            </div>

            <div class="form__group">
              <label class="form__label">
                <span class="label__text">手机</span>
                <input
                  tabindex="5"
                  id="mobile"
                  type="text"
                  maxlength="11"
                  class="input"
                  v-model="form.mobile"
                />
              </label>
            </div>
          </VerticalCenter>
        </div>
      </form>
      <div style="margin-top: 70px; text-align: center">
        <!-- <a @click="post" href="javascript:void(0);" class="button"
          >生成邮件签名</a
        > -->
        <a
          @click="post"
          href="javascript:void(0);"
          class="button button-generate"
          >生成邮件签名</a
        >
      </div>
    </BoxContent>

    <div class="ui-mask"></div>
  </div>
</template>

<script>
import Croppie from "@/utils/croppie";
// const Croppie = window.Croppie
import "@/utils/qrcode-reader";
import QRious from "@/utils/qrious.min";
import html2canvas from "@/utils/html2canvas.min";
import BoxHead from "@/components/BoxHead.vue";
import VerticalCenter from "@/components/VerticalCenter.vue";
import BoxContent from "@/components/BoxContent.vue";
import IconSuccess from "@/components/IconSuccess.vue";
import qrcodeDemo from "@/assets/img/qrcode.png";
import avatarDemo from "@/assets/img/imgPreview.png";
import tipImg from "@/assets/img/tips.gif";
import ImageCompressor from "image-compressor.js";
import axios from "axios";

function readFile(input, cb) {
  if (input.files && input.files[0]) {
    var reader = new FileReader();

    reader.onload = function(e) {
      cb(e.target.result);
    };

    reader.readAsDataURL(input.files[0]);
  } else {
    alert("文件读取不成功");
  }
}

var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
function fileChange(target) {
  var fileSize = 0;
  var filetypes = [".jpg", ".png", ".jpeg", ".gif"];
  var filepath = target.value;
  var maxMB = 10;
  var filemaxsize = 1024 * maxMB; //2M
  if (filepath) {
    var isnext = false;
    var fileend = filepath.substring(filepath.lastIndexOf(".")).toLowerCase();
    for (var i = 0; i < filetypes.length; i++) {
      if (filetypes[i] == fileend) {
        isnext = true;
        break;
      }
    }
    if (!isnext) {
      alert("文件类型错误，请上传 jpg, jpeg, png, gif 格式的图片！");
      target.value = "";
      return false;
    }
  }
  if (isIE && !target.files) {
    var filePath = target.value;
    var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
    if (!fileSystem.FileExists(filePath)) {
      alert("附件不存在，请重新输入！");
      return false;
    }
    var file = fileSystem.GetFile(filePath);
    fileSize = file.Size;
  } else {
    fileSize = target.files[0].size;
  }

  // fileSize 是 b 单元
  var size = fileSize / 1024;
  if (size > filemaxsize) {
    alert("附件大小不能大于" + maxMB + "M！");
    target.value = "";
    return false;
  }
  if (size <= 0) {
    alert("附件大小不能为0M！");
    target.value = "";
    return false;
  }
  return true;
}
export default {
  components: {
    BoxHead,
    BoxContent,
    VerticalCenter,
    IconSuccess,
  },
  props: ["team"],
  data() {
    return {
      notShowQrcodeUploader: true,
      showDepartSelect: true,
      form: {
        occupation: "",
        tel: "",
        mobile: "",
        rtxName: window.rtx + " " + window.userData.ChnName || "",
        location: "sz",
        locationString: "",
        depart: "",
        pretel: "0755 - 86013388 - ",
        customizeDepart: "",
        customizeLocation: "",
      },
      departs: {
        微信广告部: "微信广告部",
        交易广告部: "交易广告部",
        平台与内容广告部: "平台与内容广告部",
        联盟广告部: "联盟广告部",
        商业内容中心: "商业内容中心",
        行业销售运营一部: "行业销售运营一部",
        行业销售运营二部: "行业销售运营二部",
        行业销售运营三部: "行业销售运营三部",
        行业销售运营四部: "行业销售运营四部",
        行业销售运营五部: "行业销售运营五部",
        渠道生态合作部: "渠道生态合作部",
        区域发展部: "区域发展部",
        开放链路交付部: "开放链路交付部",
        闭环链路交付部: "闭环链路交付部",
        商业平台部: "商业平台部",
        广告数据部: "广告数据部",
        平台产品技术部: "平台产品技术部",
        交易基建部: "交易基建部",
        商业管理部: "商业管理部",
        广告市场部: "广告市场部",
        广告营销设计中心: "广告营销设计中心",
        CDG_AMS风控组: "CDG-AMS风控组",
        腾讯广告统一计费财务联合项目组: "CDG腾讯广告统一计费财务联合项目组",
        海外发展中心: "海外发展中心",
        泛生态业务部: "泛生态业务部",
        "广告营销服务线（跨部门请选择此项）": "广告营销服务线",
        _customize: {
          label: "<自定义>",
          value: "_customize",
        },

        // 新闻视频广告合作部: "新闻视频广告合作部",
        // 信息流与QQ广告部: "信息流与QQ广告部",
        // 交付运营中心: "交付运营中心",
        // 营销赋能中心: "营销赋能中心",
        // 广告安全管理中心: "广告安全管理中心",
      },
      locations: {
        sz: "深圳·腾讯滨海大厦",
        sztx: "深圳·腾讯大厦",
        szds: "深圳·松日鼎盛大厦",
        szjd: "深圳·金地威新中心",
        bj: "北京·银科大厦",
        bjxgm: "北京·希格玛大厦",
        bjlh: "北京·领航科技大厦",
        bjsouh: "北京·搜狐网络大厦",
        bjyzjr: "北京·亚洲金融大厦",
        gz: "广州·TIT创意园",
        gzmtg: "广州·媒体港大厦",
        shbj: "上海·腾讯滨江大厦",
        shnb: "上海·诺布大厦",
        sh: "上海·腾云大厦",
        shtx: "上海·腾讯大厦",
        shxd: "上海·现代服务园",
        cd: "成都·腾讯大厦",
        _customize: {
          label: "<自定义>",
          value: "_customize",
        },
      },
      isPosting: false,
      // createdImg: 'http://pan.oa.com/wxad/signature_maker/max.png',
      createdImg: "",
      croppieInstance: null,
      croppieInstanceCode: null,
      avatarSuccess: false,
      avatarData: avatarDemo,
      tipImg: tipImg,
      qrcodeSuccess: false,
      qrcodeData: qrcodeDemo,
      codeDataUrl: "",
      faqLink: "#/MailAbout",
      imgData: "",
    };
  },
  methods: {
    initCroppie(url) {
      if (this.croppieInstance) {
        this.croppieInstance.destroy();
      }

      this.croppieInstance = $(this.$refs.imageArea).croppie({
        viewport: {
          width: 250,
          height: 250,
          type: "circle",
        },
        boundary: {
          width: 300,
          height: 300,
        },
        // maxZoom: 1,
        customClass: "",

        // enforceBoundary: true,
        enableZoom: true, //default true // previously showZoom
        showZoomer: true, //default true
        mouseWheelZoom: false, //default true
      });
      // this.croppieInstance.croppie('bind', {
      //   url: 'http://dayu.oa.com/avatars/yexiao/profile.jpg'
      // })
    },
    initCroppieCode(url) {
      if (this.croppieInstanceCode) this.croppieInstanceCode.destroy();

      this.croppieInstanceCode = $(this.$refs.codeArea).croppie({
        viewport: {
          width: 250,
          height: 250,
        },
        boundary: {
          width: 300,
          height: 300,
        },
        // maxZoom: 1,
        customClass: "",
        // enforceBoundary: true,
        enableZoom: true, //default true // previously showZoom
        showZoomer: true, //default true
        mouseWheelZoom: false, //default true
      });
    },

    alert(txt) {
      setTimeout(function() {
        alert(txt);
      }, 0);
    },
    post() {
      const {
        rtxName,
        occupation,
        tel,
        mobile,
        location,
        depart,
        pretel,
        customizeDepart,
        customizeLocation,
      } = this.form;

      if (!occupation || !rtxName) {
        this.alert("请填写完整");
        return false;
      }

      if (this.showDepartSelect && depart !== "_customize" && !depart) {
        this.alert("请选择中心/小组");
        return false;
      }

      if (
        this.showDepartSelect &&
        depart === "_customize" &&
        !customizeDepart
      ) {
        this.alert("请输入自定义部门名称");
        return false;
      }

      if (location === "_customize" && !customizeLocation) {
        this.alert("请输入自定义地址");
        return false;
      }

      if (!mobile || mobile.length != 11) {
        this.alert("请输入正确的手机号码");
        return false;
      }

      // if (!tel || tel.length != 6) {
      //   this.alert("请输入正确的座机号码");
      //   return false;
      // }

      if (!this.avatarSuccess) {
        this.alert("请上传头像");
        return false;
      }

      if (!this.qrcodeSuccess) {
        this.alert("请上传你的微信二维码");
        return false;
      }

      if (this.isPosting) return false;

      const tel2 = tel ? pretel + tel : "";
      // alert('pretel:' + pretel + 'tel' + tel2)
      const mobile2 =
        mobile.slice(0, 3) +
        " " +
        mobile.slice(3, 7) +
        " " +
        mobile.slice(7, 11);
      var data = {
        rtxName: rtxName.charAt(0).toUpperCase() + rtxName.slice(1),
        occupation,
        tel: tel2,
        mobile: mobile2,
        avatarData: this.avatarData,
        qrcodeData: this.qrcodeData,
        departString:
          this.form.depart === "_customize"
            ? this.form.customizeDepart
            : this.departs[this.form.depart],
        team: this.team,
        locationString:
          this.form.location === "_customize"
            ? this.form.customizeLocation
            : this.locations[this.form.location],
      };
      this.imgData = data;
      this.toImage(this.imgData);
    },
    initQrcodeUploader() {
      const that = this;
    },
    resetForm() {
      this.createdImg = "";
    },
    changeLoc() {
      const formList = {
        sz: "0755 - 86013388 - ",
        gz: "020 - 81167888 - ",
        bj: "010 - 62671188 - ",
        sh: "021 - 54569595 - ",
        cd: "028 - 85225111 - ",
      };
      Object.keys(formList).forEach((value) => {
        console.log(this.form.location);
        if (this.form.location.indexOf(value) !== -1) {
          this.form.pretel = formList[value];
        }
      });
    },
    toImage() {
      setTimeout(function() {
        const dom = $(".creatSign-layout");
        const containerDom = $(".router-view-content");
        console.log(dom.offset(), "num");
        html2canvas(document.querySelector(".creatSign-layout"), {
          scale: 2,
          dpi: 300,
          x: dom.offset().left,
          y: dom.offset().top,
          width: 556,
          height: 168,
          removeContainer: true,
        }).then(function(canvas) {
          let dataURL = canvas.toDataURL("image/jpg");
          // console.log(dataURL);
          function base64ToBlob(img) {
            let parts = img.split(";base64,");
            let contentType = parts[0].split(":")[1];
            let raw = window.atob(parts[1]);
            let rawLength = raw.length;
            let uInt8Array = new Uint8Array(rawLength);
            for (let i = 0; i < rawLength; ++i) {
              uInt8Array[i] = raw.charCodeAt(i);
            }
            return new Blob([uInt8Array], { type: contentType });
          }

          const blob = base64ToBlob(dataURL);
          let evt = document.createEvent("HTMLEvents");
          evt.initEvent("click", true, true);
          let aLink = document.createElement("a");
          aLink.download = "邮件签名";
          aLink.href = URL.createObjectURL(blob);
          aLink.click();
        });
      });
    },
  },
  mounted() {
    let that = this;
    that.initCroppie();
    that.initCroppieCode();
    // that.toImage();

    $("#avatar-upload").on("change", function(ev) {
      if (fileChange(ev.target)) {
        $(".ui-mask").show();
        $("#avatar-crop").show();
        readFile(this, (dataurl) => {
          that.croppieInstance.croppie("bind", {
            url: dataurl,
          });

          $("#upload-demo").addClass("ready");
          $("#avatar-upload").val(null);
        });
      }
    });

    $("#upload-avatar-result").on("click", (ev) => {
      this.croppieInstance
        .croppie("result", { type: "canvas", size: "viewport" })
        .then(function(resp) {
          $(".ui-mask").hide();
          console.log(resp);
          that.avatarData = resp;

          $(".avatar-crop").hide();
          that.avatarSuccess = true;
        });
    });

    $("#qrcode-upload").on("change", function(ev) {
      if (fileChange(ev.target)) {
        $(".ui-mask").show();
        $("#qrcode-crop").show();
        readFile(this, (dataurl) => {
          that.croppieInstanceCode.croppie("bind", {
            url: dataurl,
          });

          $("#upload-code").addClass("ready");
          $("#qrcode-upload").val(null);
        });
      }
    });

    $("#upload-qrcode-result").on("click", (ev) => {
      this.croppieInstanceCode.croppie("result", "canvas").then(function(resp) {
        $(".ui-mask").hide();
        $("#qrcode-crop").hide();
        // alert(resp);
        // alert(dataurl);

        that.qrcodeSuccess = false;
        const dataurl = that.codeDataUrl;

        qrcode.callback = function(decodedData) {
          if (decodedData.indexOf("error decoding QR Code") == -1) {
            const qrious = new QRious({
              value: decodedData,
              size: 72,
            });
            that.qrcodeData = qrious.toDataURL();
            that.qrcodeSuccess = true;
          } else {
            that.qrcodeData = qrcodeDemo;
            that.alert(
              "没有检测到二维码，请重新生成常规黑白微信二维码,保存为原图上传或查看使用说明。"
            );
          }
        };
        qrcode.decode(resp);
      });
    });
    // this.initQrcodeUploader()
  },
  watch: {
    "form.depart": {
      handler(newVal, oldVal) {
        console.log(this.form);
      },
    },
  },
};
</script>
<style lang="scss" scoped>
$num: 1;

.creatSign {
  position: absolute;
  margin: 0 auto;
  width: 100%;
  height: 160px;
  z-index: -1;
  // z-index: 11;
  // visibility: hidden;
}

.creatSign-layout {
  // width: 548px * $num;
  width: 556px;
  height: 168px;
  // border: 1px * $num solid #e1e7eb;
  // background: #fff;
  background: url(../assets/signBg.png);
  background-size: 556px 168px;
  // border-radius: 8px * $num;
  // height: 160px * $num;
  transform-origin: center;
  position: absolute;
  left: 50%;
  top: 50%;
  // transform: translate(-50%, -50%) scale(0.5);
  transform: translate(-50%, -50%);
  padding: 24px * $num 28px * $num;
  // box-shadow: 0 0 4px 0 rgba(0,0,0,0.20);
}

.creatSign__department-layer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px * $num solid #e1e7eb;
  padding-bottom: 13px;
}

.creatSign__department {
  font-size: 12px * $num;
  height: 22px;
  color: #757575;
  line-height: normal;
  display: flex;
  align-items: center;
}

.creatSign__person-layout,
.createSign__person-info {
  display: flex;
  align-items: center;
}

.creatSign__person-layout {
  justify-content: space-between;
  padding-top: 10px * $num;
}

.creatSign__image {
  height: 70px * $num;
  width: 70px * $num;
  margin-right: 16px * $num;
  img {
    width: 100%;
    height: 100%;
    border: 1px * $num solid #e7e7e7;
    box-sizing: border-box;
    height: 70px * $num;
    width: 70px * $num;
    border-radius: 50%;
  }
}

.createSign__qrcode {
  height: 60px * $num;
  width: 60px * $num;
  margin-right: 10px * $num;
  img {
    width: 100%;
    height: 100%;
    height: 60px * $num;
    width: 60px * $num;
  }
}

$icons: ("tel", "phone", "location");

@for $i from 1 through length($icons) {
  .icon-#{nth($icons,$i)} {
    display: block;
    height: 12px * $num;
    width: 12px * $num;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.createSign__contact__icon {
  height: 12px * $num;
  width: 12px * $num;
  // display: flex;
  // align-items: center;
  // top: 0;
  // left: 0;
  // position: absolute;
  img {
    width: 100%;
    height: 100%;
    // line-height: 1;
    // display: block;
  }
}

.createSign__contact-info {
  display: flex;
  align-items: center;
}

.createSign__contact-li {
  padding-left: 12px;
  display: flex;
  align-items: center;
  line-height: 1;
  color: #657575;
  height: 12px;
  flex: 1;
  min-height: 0;

  &:first-child {
    top: 2px;
  }

  // &:nth-child(2) {
  //   top: 22px;
  // }
  // &:nth-child(3) {
  //   top: 42px;
  // }
  &:last-child {
    margin-bottom: 4px;
  }
  // & + .createSign__contact-li {
  //   margin-top: 10px;
  // }
}

.createSign__contact-text {
  margin-left: 4px * $num;
  white-space: nowrap;
  font-family: inherit;
  line-height: 16px;
  font-size: 12px;
  text-overflow: ellipsis;
  overflow: hidden;
}

.createSign__contact-ways {
  font-size: 12px * $num;
  line-height: 12px;
  width: 186px;
  height: 75px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.createSign__contact__icon_img {
  top: 2px;
  position: relative;
  width: 12px;
  height: 12px;
}

.creatSign__name {
  font-size: 16px * $num;
  color: #212121;
  line-height: 1;
  font-weight: bold;
}

.creatSign__job {
  font-size: 14px * $num;
  color: #212121;
  line-height: 1;
  margin-top: 9px;
}

.button {
  display: inline-block;
  background: #2a3b5e;
  border-radius: 40px;
  font-size: 16px;
  color: #fff;
  letter-spacing: 0;
  line-height: 48px;
  padding: 0 48px;
}

.button-hover-trigger:hover .button,
.button:hover {
  text-decoration: none;
  background: #0a1533;
  color: #fff;
}

.button-small {
  background: #fff;
  border: 1px solid #0a1533;
  border-radius: 40px;
  font-size: 14px;
  color: #0a1533;
  letter-spacing: 0;
  line-height: 34px;
  padding: 0 16px;
}

.button-hover-trigger:hover .button-small,
.button-small:hover {
  border-color: #0a1533;
}

.button-gray {
  background: #fff;
  border: 1px solid #d4dade;
  color: #9ca1a6;
}

.uploader__avatar {
  position: relative;
  display: inline-block;
  margin-bottom: 28px;
  input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer; // z-index: -1;
  }
}

.form {
  zoom: 1;
  &::after {
    clear: both;
    content: ".";
    display: block;
    width: 0;
    height: 0;
    visibility: hidden;
  }
}

.flex {
  height: 250px;
  float: left;
}

.flex:not(:first-child) {
  border-left: 1px solid #e1e7eb;
}

.avatar {
  width: 270px;
  img {
    width: 80px;
  }
}

.qrcode {
  width: 269px;
  img {
    width: 80px;
  }
}

.others {
  width: 363px;
  .inlineBlock {
    width: 300px;
  }
}

.uploader-preview {
  height: 100px;
  margin-top: 10px;
  margin-bottom: 16px;
}

.qrcode .uploader-preview {
  height: 100px;
}

.upload-desc {
  color: #a6a6a6;
  text-align: left;
  line-height: 20px;
  position: relative;
}

.avatar .upload-desc {
  padding: 0 70px;
}

.qrcode .upload-desc {
  padding: 0 20px 0 36px;
}

.upload-desc li::before {
  position: absolute;
  margin-left: -10px;
  margin-top: 7px;
  content: " ";
  width: 4px;
  height: 4px;
  border-radius: 2px;
  background: #d4dade;
}

::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #a6a6a6;
}

::-moz-placeholder {
  /* Firefox 19+ */
  color: #a6a6a6;
}

:-ms-input-placeholder {
  /* IE 10+ */
  color: #a6a6a6;
}

:-moz-placeholder {
  /* Firefox 18- */
  color: #a6a6a6;
}

.form__group {
  width: 295px;
  border-bottom: 1px solid #e1e7eb;
  padding-bottom: 8px;
  &:not(:last-child) {
    margin-bottom: 17px;
  }
  label {
    font-size: 15px;
    color: #212121;
    letter-spacing: 0;
  }
  .label__text {
    display: inline-block;
    width: 52px;
  }
  input {
    border: none;
  }
  .input {
    width: 190px;
    border: none;
    padding-left: 0;
  }
  .input_capitalize {
    text-transform: capitalize;
  }
  .select-dep,
  .select {
    cursor: pointer;
    border: none;
    background-color: transparent;
    border-radius: 0;
    outline: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    padding-right: 24px;
    position: relative;
    background: url("../assets/img/arrow.svg");
    background-size: 14px 8px;
    background-repeat: no-repeat;
    background-position: right 4px;
  }
  .select-dep {
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    option {
      width: 160px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

option[disabled],
option:disabled,
option.disabled {
  color: #333 !important;
  -webkit-text-fill-color: #333 !important;
  -webkit-opacity: 1;
  opacity: 1;
}

.result {
  text-align: center;
}

.result-img {
  margin-top: 100px;
  margin-bottom: 40px;
}

.result-info {
  width: 548px;
  margin: 0 auto;
  text-align: left;
  color: #949494;
}

// 遮罩层
.ui-mask {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: #000;
  opacity: 0.6;
  filter: alpha(opacity=50);
  z-index: 15;
  display: none;
}

.demo-img {
  position: relative;
  top: -20px;
  width: 556px;
}

.tips-block {
  display: inline-block;
  position: relative;
}

.tips-img {
  display: none;
  position: absolute;
  top: 39px;
  left: 26px;
  transform: translateY(-100%);
  z-index: 10;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 15px 0;
  padding: 10px;

  &:after {
    content: "";
    position: absolute;
    left: -8px;
    bottom: 19px;
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-right: 10px solid #fff;
    border-bottom: 10px solid transparent;
  }
  img {
    display: inline-block;
    width: 450px;
  }
  .tips-mainblock {
    overflow: hidden;
  }
}

.tips {
  display: inline-block;
  width: 14px;
  height: 14px;
  font-size: 10px;
  position: relative;
  top: 2px;
  left: 0px;
  background: url("../assets/img/tip_hover.png");
  background-size: 14px auto;
  background-repeat: no-repeat;
  cursor: pointer;

  &:hover {
    background: url("../assets/img/tip_hover.png");
    background-size: 14px auto;
    background-repeat: no-repeat;

    & + .tips-img {
      display: inline-block;
    }
  }
}
.fullwidth {
  width: 1000px;
  margin: 0 auto;
}

.clearfix {
  zoom: 1;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
.clearfix:before {
  content: "";
  display: block;
}

.pull-left {
  float: left;
}
.pull-right {
  float: right;
}

.divide {
  height: 0;
  width: 100%;
  border-bottom: 1px #eef0f4 solid;
}
a {
  color: #459ae9;
  text-decoration: none;
}
</style>
<style>
canvas {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}
.avatar-crop {
  display: none;
  position: relative;
  z-index: 9999;
}
.avatar-crop-inner {
  position: absolute;
  width: 400px;
  height: 500px;
  left: 50%;
  margin-left: -200px;
  top: 0;
  background-color: #fff;
  z-index: 9999;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  text-align: center;
}
.croppie-container {
  padding: 45px 0;
}
.croppie-container .cr-image {
  z-index: -1;
  position: absolute;
  top: 0;
  left: 0;
  transform-origin: 0 0;
  max-width: none;
}

.croppie-container .cr-boundary {
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  z-index: 1;
}

.croppie-container .cr-viewport {
  position: absolute;
  border: 2px solid #fff;
  margin: auto;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  box-shadow: 0 0 0 899px rgba(0, 0, 0, 0.5);
  z-index: 0;
}
.croppie-container .cr-vp-circle {
  border-radius: 50%;
}
.croppie-container .cr-overlay {
  z-index: 1;
  position: absolute;
  cursor: move;
}
.croppie-container .cr-slider-wrap {
  /* width: 75%; */
  width: 225px;
  margin: 0 auto;
  margin-top: 20px;
  text-align: center;
}
.croppie-result {
  position: relative;
  overflow: hidden;
}
.croppie-result img {
  position: absolute;
}

/*************************************/
/***** STYLING RANGE INPUT ***********/
/*************************************/
/*http://brennaobrien.com/blog/2014/05/style-input-type-range-in-every-browser.html */
/*************************************/

.cr-slider {
  -webkit-appearance: none; /*removes default webkit styles*/
  /*border: 1px solid white; */ /*fix for FF unable to apply focus style bug */
  width: 300px; /*required for proper track sizing in FF*/
  max-width: 100%;
}
.cr-slider::-webkit-slider-runnable-track {
  width: 100%;
  height: 3px;
  background: rgba(0, 0, 0, 0.5);
  border: 0;
  border-radius: 3px;
}
.cr-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  border: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ddd;
  margin-top: -6px;
}
.cr-slider:focus {
  outline: none;
}
/*
.cr-slider:focus::-webkit-slider-runnable-track {
    background: #ccc;
}
*/

.cr-slider::-moz-range-track {
  width: 100%;
  height: 3px;
  background: rgba(0, 0, 0, 0.5);
  border: 0;
  border-radius: 3px;
}
.cr-slider::-moz-range-thumb {
  border: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ddd;
  margin-top: -6px;
}

/*hide the outline behind the border*/
.cr-slider:-moz-focusring {
  outline: 1px solid white;
  outline-offset: -1px;
}

.cr-slider::-ms-track {
  width: 300px;
  height: 5px;
  background: transparent; /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */
  border-color: transparent; /*leave room for the larger thumb to overflow with a transparent border */
  border-width: 6px 0;
  color: transparent; /*remove default tick marks*/
}
.cr-slider::-ms-fill-lower {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
}
.cr-slider::-ms-fill-upper {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
}
.cr-slider::-ms-thumb {
  border: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ddd;
}
.cr-slider:focus::-ms-fill-lower {
  background: rgba(0, 0, 0, 0.5);
}
.cr-slider:focus::-ms-fill-upper {
  background: rgba(0, 0, 0, 0.5);
}
/*******************************************/

/* Just cross hairs for debugging - can be removed upon release */
.croppie-container .cr-viewport.debug:before,
.croppie-container .cr-viewport.debug:after {
  background: white;
  width: 1px;
  height: 1px;
  content: "";
  position: absolute;
}
.croppie-container .cr-viewport.debug:before {
  top: 0;
  height: 100%;
  left: 50%;
}
.croppie-container .cr-viewport.debug:after {
  top: 50%;
  left: 0;
  width: 100%;
}

.html2canvas_logo {
  height: 18px;
  width: 100px;
  position: absolute;
}
</style>
