<script>

  export default {
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '二级品牌标志',
          content: ''
        },
        info: [
          {
            title: '二级品牌架构',
            content: [
              '腾讯广告品牌架构由两级组成，分为广告场景和广告生态两部分。二级品牌中部分品牌带有独立对外传播的标志与名称，部分仅有名称无独立标志。所有二级品牌标志在使用过程中，只能直接使用资源包的文件，不可更改二级品牌标志中文字部分。在使用过程中请严格遵循规则。'
            ],
            img: [],
          },
          {
            title: '二级品牌字体设计规范',
            content: [
              '为保证腾讯广告的视觉统一，二级品牌标志的文字标志设计都应与腾讯广告文字字体保持一直，具体设计基于“腾讯体W7”的结构左倾斜-8°，对齐“腾讯广告”字形做细节修整。',
            ],
            img: [
              require("../assert/VI/sub/<EMAIL>"),
              require("../assert/VI/sub/<EMAIL>"),
              require("../assert/VI/sub/<EMAIL>"),
              require("../assert/VI/sub/<EMAIL>"),
            ],
          },
          {
            title: '二级品牌组合规范',
            content: [
              '二级品牌标志与主标志的并置有严格规范，该组合标志的大小、比例、间距都应按照一定的规则组成，不可随意更改。在样式上分为独立标志并置和字标并置两种。为保证品牌传播的一致性，组合标志里的标志采用中文标志。字标并置中的字体，应根据二级品牌字体设计规则设计，且大小、字色应与腾讯广告标志中的字标保持一致，双行文字大小应该与并置分割线高度保持一致。',
            ],
            img: [
              require("../assert/VI/sub/<EMAIL>"),
              require("../assert/VI/sub/<EMAIL>"),
            ],
          },
          {
            title: '二级品牌设计版式规范',
            content: [
              '标志的位置取决于标志的样式。标准标志在版式页面中应遵循左对齐或右对齐，居顶、居中或居下。例如这种版式运用在大多数办公应用系统。垂直标志应遵循对齐，居顶或居下。而图形标志应遵循左中右对齐，居上、居中或居下。',
            ],
            img: [
              require("../assert/VI/sub/<EMAIL>"),
              require("../assert/VI/sub/<EMAIL>"),
            ],
          },
        ],
      }
    }
  }
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <TsaInfo :info="info"></TsaInfo>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav
     title-left="图像" link-left="/Brand/BrandImg"
     title-right="Tencent in 使用规范" link-right="/Brand/BrandTencentIn"
    ></TsaInfoNav> -->

  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
