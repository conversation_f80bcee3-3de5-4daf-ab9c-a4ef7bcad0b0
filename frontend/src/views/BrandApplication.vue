<script>

  export default {
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        bannerMsg: {
          title: '品牌应用指南',
          content: [
            '所有关于腾讯广告的品牌应用，',
            '请遵循此规范以建立专业统一的品牌形象。'
          ],
          pic: require("../assert/Banner/<EMAIL>")
        },
      }
    }
  }
</script>

<template>
  <div class="main">

    <div v-if="isBlack">
      <TsaHeaderBlack ref="header" title='品牌资源' index='5' :blueBlock="blueBlock"></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader ref="header" title='品牌资源' index='5' :blueBlock="blueBlock"></TsaHeader>
    </div>


    <TsaTitle :title="bannerMsg.title" :content="bannerMsg.content" :pic="bannerMsg.pic"></TsaTitle>

    <div class="main-body">
      <TsaComingSoon></TsaComingSoon>
    </div>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>

    <!-- 回到顶部板块 -->
    <GoTop v-if="goToFixed"></GoTop>
  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
