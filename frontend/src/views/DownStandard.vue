<script>

	export default {
		data() {
			return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '品牌视觉规范',
          content: '所有关于腾讯广告的品牌视觉运用，都请参阅并遵循此规范以保持品牌形象的识别度与统一。'
        },
				info: [
					{
						title: '中文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。思源黑体做为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_01.png"),
						noboard: true
					},
					{
						title: '英文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。Daxline Offc做为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_02.png"),
						noboard: false
					}
				],
				downloadOptions: [
					{
						name: '腾讯广告标志使用规范',
						pic: require("../assert/Down/standard/<EMAIL>"),
						link: 'https://down.qq.com/tad/AMS_Brand_Identity_Guideline_V1.2.zip',
					},
					{
						name: '腾讯广告地方站团队品牌规范',
						pic: require("../assert/Down/standard/<EMAIL>"),
						link: 'http://down.qq.com/tad/2022_local_station_team_brand_specifications.pdf.zip',
					},
				],
			}
		}
	}
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <!-- 下载板块 -->
    <!-- 如果不是微信展示下方 -->
    <div class="down-body" v-if="!isWeixin">
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.link" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 如果是微信展示下方 -->
    <div class="down-body" v-if="isWeixin">
      <p class="down-title">下载主品牌标志</p>
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.wxlink" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav title-right="品牌标志" link-right="/Download"></TsaInfoNav> -->

  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
