<script>
import bannerMsgPic from "../assert/Banner/<EMAIL>";
import AdLogo from "../assets/ad_logo.png";

export default {
  created() {
    this.watchScroll();
  },
  beforeDestroy() {
    this.unWatchScroll();
  },
  data() {
    return {
      blueBlock: false,
      isBlack: false,
      bannerMsg: {
        title: "公关话术规范",
        content: [
          "所有关于腾讯广告的公关传播话术，",
          "请遵循此规范以建立专业统一的品牌形象。",
        ],
        pic: bannerMsgPic,
      },
      cardInfo: [
        // {
        //   pic: require("../assets/ad_logo.png"),
        //   options: {
        //     title: 'AMS对外沟通文件指引规范(简版)',
        //     button: { value: '打开PDF', link: 'http://amsbrand.oa.com/files/PR/AMS_extrnal_communication_guideline(short_version)1911.pdf' }
        //   },
        // },
        // {
        //   pic: require("../assets/ad_logo.png"),
        //   options: {
        //     title: 'AMS对外沟通文件指引规范(综合版)',
        //     button: { value: '打开PDF', link: 'http://amsbrand.oa.com/files/PR/AMS_extrnal_communication_guideline(full_version)1911.pdf' }
        //   },
        // },
        // {
        //   pic: require("../assets/ad_logo.png"),
        //   options: {
        //     title: 'AMS数据合规&对外沟通 培训PPT',
        //     button: { value: '打开PDF', link: 'http://amsbrand.oa.com/files/PR/AMS_data_standard_&_external_communication_training_fileQ4.pdf' }
        //   },
        // },
        // {
        //   pic: require("../assets/ad_logo.png"),
        //   options: {
        //     title: 'AMS数据合规&对外沟通 培训视频',
        //     button: { value: '前往查看', link: 'http://v8.learn.oa.com/user/net?act_id=12564' }
        //   },
        // },
        // {
        //   pic: require("../assets/ad_logo.png"),
        //   options: {
        //     title: "AMS 新闻发言人制度及特殊对外职衔使用要求",
        //     button: {
        //       value: "打开PDF",
        //       link: "https://down.qq.com/tad/2022_AMS_press_spokesman_system_&_external_title_requirements.pdf",
        //     },
        //   },
        // },
        {
          pic: AdLogo,
          options: {
            title: "对外沟通文件指引规范",
            button: {
              value: "打开PDF",
              link: "https://down.qq.com/tad/external_communication_document.pdf",
            },
          },
        },
        {
          pic: AdLogo,
          options: {
            title: "书面材料修改对照表",
            button: {
              value: "打开PDF",
              link: "https://down.qq.com/tad/writing_revised_edition.pdf",
            },
          },
        },
        {
          pic: AdLogo,
          options: {
            title: "AMS Iwiki 标准词条定义及释义",
            button: {
              value: "打开文档",
              link: "https://doc.weixin.qq.com/sheet/e3_AFgA1wZ-ACc8R6bzJSaT7Cv7GvDX2?scode=AJEAIQdfAAoshMV8mNAMwAfgZ3ACg",
            },
          },
        },
        // {
        //   pic: AdLogo,
        //   options: {
        //     title: 'TDC危机公关预防方案',
        //     button: { value: '打开PDF', link: 'http://amsbrand.oa.com/files/PR/TDC_crisis_PR_prevention.pdf' }
        //   },
        // },
      ],
    };
  },
};
</script>

<template>
  <div class="main">
    <div v-if="isBlack">
      <TsaHeaderBlack
        ref="header"
        title="品牌资源"
        index="1"
        :blueBlock="blueBlock"
      ></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader
        ref="header"
        title="品牌资源"
        index="1"
        :blueBlock="blueBlock"
      ></TsaHeader>
    </div>

    <TsaTitle
      ref="titleBlock"
      :title="bannerMsg.title"
      :content="bannerMsg.content"
      :pic="bannerMsg.pic"
    ></TsaTitle>

    <div class="small-card-body">
      <div
        class="card-container"
        v-for="(item, index) in cardInfo"
        :key="index"
      >
        <TsaSmallCard :pic="item.pic" :options="item.options"></TsaSmallCard>
      </div>
    </div>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>

    <!-- 回到顶部板块 -->
    <GoTop v-if="goToFixed"></GoTop>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";
@keyframes BannerOpacity1 {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

.small-card-body {
  margin: 64px auto auto auto;
  max-width: 1200px;
  &:after {
    content: "";
    display: block;
    clear: both;
  }

  .card-container {
    /*display: inline-block;*/
    float: left;
    width: 380px;
    margin: 0 30px 0 0;
    overflow: hidden;
    background-image: url("../assert/Public/pr_nbgc.png");
    background-size: 382px auto;
    background-repeat: no-repeat;
    background-origin: border-box;
    border: 2px solid #e0eaff;
    box-shadow: 0 4px 20px 0 rgba(0, 116, 255, 0.08);
    border-radius: 12px;
    transition: all 0.2s;
    opacity: 0;
    animation: BannerOpacity1 1s ease forwards;

    &:hover {
      border: 2px solid #296bef;
      box-shadow: 0 1px 28px 0 rgba(44, 114, 255, 0.1),
        0 4px 20px 0 rgba(0, 116, 255, 0.08);
    }

    &:nth-child(n + 4) {
      margin-top: 30px;
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
  }

  @media (max-width: $screen-lg) {
    max-width: 984px;

    .card-container {
      margin: 0 24px 0 0;
      width: 312px;
      background-size: 316px auto;

      &:nth-child(n + 4) {
        margin-top: 24px;
      }
    }
  }
}
</style>
