<script>

	export default {
		data() {
			return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '品牌标准字',
          content: '所有关于腾讯广告设计输出物的字体运用，请下载官方文件以保持品牌形象的识别度。'
        },
				info: [
					{
						title: '中文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。方正悠黑做为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_01.png"),
						noboard: true
					},
					{
						title: '英文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。Daxline Offc做为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_02.png"),
						noboard: false
					}
				],
				downloadOptions: [
					{
						name: '方正悠黑',
						pic: require("../assert/Down/font/Down_font_fangzheng.png"),
						link: 'https://down.qq.com/tad/AMS_Standard_Chin_Font.zip',
						desc: [
							'版权使用范围：',
							'1.方正悠黑可以不受限制的免费使用',
							'2.采用Apache 2.0许可证授权',
						],
					},
					{
						name: 'Helvetica',
						pic: require("../assert/Down/font/<EMAIL>"),
						link: 'https://down.qq.com/tad/Helvetica_Neue.zip',
						desc: [
							'版权使用范围：',
							'仅腾讯广告业务与部门可使用helvecita字体',
						],
					},
					{
						name: '腾讯体',
						pic: require("../assert/Down/font/<EMAIL>"),
						link: 'https://down.qq.com/tad/TencentSans.zip',
						desc: [
							'版权使用范围：',
							'1.腾讯公司所属业务与部门可使用腾讯体',
							'2.腾讯公司投资企业可使用腾讯体',
							'警告：超出权益适用范围，腾讯法务部将追究法律责任',
						],
					},
				],
			}
		}
	}
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <!-- 下载板块 -->
    <!-- 如果不是微信展示下方 -->
    <div class="down-body" v-if="!isWeixin">
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.link" :name="item.name" :desc="item.desc"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 如果是微信展示下方 -->
    <div class="down-body" v-if="isWeixin">
      <p class="down-title">下载主品牌标志</p>
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.wxlink" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav
     title-right="PPT 模板" link-right="/Download/DownPPT"
     title-left="品牌标志" link-left="/Download"
    ></TsaInfoNav> -->

  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
