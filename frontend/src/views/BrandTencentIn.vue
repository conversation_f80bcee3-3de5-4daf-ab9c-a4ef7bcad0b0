<script>

  export default {
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: 'Tencent in 使用规范',
          content: '所有Tencennt in 的品牌标志使用，都请遵循此规范以保持品牌形象的识别度与统一。'
        },
        info: [
          {
            title: '中英文双语标志',
            content: [
              '腾讯智慧营销中英文双语标志由两部分元素构成：图形标志、双语字标。标志元素间的大小及位置是固定的，且双语字标不能单独使用。标志只能从规范文件拷贝使用，不能重新绘制或擅自组合。'
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
          {
            title: '中文标志',
            content: [
              '腾讯智慧营销中文版标志由两部分元素构成：图形标志、中文字标。标志元素间的大小及位置是固定的，且中文字标不能单独使用。标志只能从规范文件拷贝使用，不能重新绘制或擅自组合。',
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
          {
            title: '腾讯智慧营销峰会标志',
            content: [
              '腾讯智慧营销峰会标志是经过设计的一个完整的整体。任何情况下都不得改变其形状、结构和比例，并保证品牌标志在应用环境中能清晰可辨，只能从规范文件拷贝使用，不能重新绘制或擅自组合。',
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
          {
            title: '安全间距',
            content: [
              '在任何时候，腾讯智慧营销的标志周围都要留有一定的安全间距，该间距范围内不得出现任何图片和文字。您可以使用字标的高度来确定标志周围的安全间距下限。',
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
          {
            title: '标志背景色',
            content: [
              '为保证品牌统一的视觉体验，使用时请根据实际使用场景选择，但标志应该始终是易读易认的。',
              '在比20%灰度更浅的背景上应使用浅色底全彩标志，在比30%灰度更深的背景应该使用深色底全彩标志。',
              '单色场景下，在比60%灰度的背景上建议使用深色标志，在比30%灰度更深的背景应该使用反白标志。',
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
          {
            title: '标志使用的禁止事项',
            content: [
              '为保证品牌标志在传播与识别的完整性及统一性，请勿擅自更改标志。下面列举几个禁止对腾讯智慧营销标志执行的操作。',
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
          {
            title: '联合标志规范',
            content: [
              '当腾讯广告与腾讯智慧营销组合出现时，标志元素间的大小及位置是固定的。组合标志遵循二级品牌标志组合规范，且组合标志只能从规范文件拷贝使用，不能重新绘制或擅自组合。',
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
          {
            title: 'Slogan',
            content: [
              '“ Interpersonal · Integrated · Intelligent ”作为腾讯智慧营销的Solgn，在配合 Tencent in 标志出现时，应严格遵循组合规范及不可侵范围，只能从规范文件拷贝使用，不能重新绘制或擅自组合。',
            ],
            img: [
              require("../assert/VI/tencent_in/<EMAIL>"),
            ],
          },
        ],
      }
    }
  }
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <TsaInfo :info="info"></TsaInfo>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
