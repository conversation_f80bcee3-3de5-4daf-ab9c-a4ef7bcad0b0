<script>

  export default {
    methods: {
      goTwo() {
        let second = this.$refs.secondary.offsetTop;
        alert(second);
        document.documentElement.scrollTop = document.body.scrollTop  = second;
      }

    },
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        info: [
          {
            title: '通用标志',
            content: [
              '腾讯广告通用标志由三个元素组成: 1.图形标志  2.中文字标  3.英文字标，其元素间的相对大小和位置是固定的，不可随意调整标志的形状、元素或比例。通用标志适用于腾讯广告品牌单独使用或与行业平级品牌共同出现的场景。',
            ],
            img: require("../assets/sign/sign_01.png"),
            noboard: true
          },
          {
            title: '中文标志',
            content: [
              '腾讯广告中文的标志通过两个元素组成: 1.图形标志  2.中文字标，其元素间的相对大小和位置是固定的，不可随意调整标志的形状、元素或比例。中文标志适用于品牌并置或无外语需求的场景，在小面积区域如中文站点的导航侧也可使用此中文标志。',
            ],
            img: require("../assets/sign/sign_02.png"),
            noboard: true
          },
          {
            title: '英文标志',
            content: [
              '腾讯广告英文的标志通过两个元素组成: 1.图形标志  2.英文字标，其元素间的相对大小和位置是固定的，不可随意调整标志的形状、元素或比例。英文标志适用于海外品牌推广场景，禁止在中文语境里单独使用此英文标志。',
            ],
            img: require("../assets/sign/sign_03.png"),
            noboard: true
          },
          {
            title: '标志网格',
            content: [
              '腾讯广告的品牌标志里每个元素及大小皆由严格的比例组成，在标志使用时不可改变标志任何元素的组合及大小。标志的安全区域为标志图形里的“四分之一圆环”大小，当标志尺寸被缩放使用时, 安全区域大小也该随之等比缩放。',
            ],
            img: require("../assets/sign/sign_04.png"),
            noboard: true
          },
          {
            title: '安全区域',
            content: [
              '为确保品牌标志可以在所有应用中被清晰识别, 标志周围需预留一定的安全不可侵犯区域。此区域相当于标志在运用时, 与其他设计元素或文本内容所应保持的最小距离。为确保标志的完整性与可读性, 此区域大小必须严格遵守规范。当标志尺寸被缩放使用时, 安全区域大小随之等比缩放。',
            ],
            img: require("../assets/sign/sign_05.png"),
            noboard: true
          },
          {
            title: '错误使用示范',
            content: [
              '为了保证品牌标志的完整性和统一性，遵循本规范中所述的使用与限制是很重要的。在使用品牌标志时，应避免如右图所示的各种不规范使用样式，以保证品牌标志传播与识别的一致性。',
            ],
            img: require("../assets/sign/sign_06.png"),
            noboard: true
          },
          {
            title: '背景颜色',
            content: [
              '全彩标志，建议做为代表使用标志，在背景色条件允许的条件下尽量使用全彩标志，但也应注意字标在不同背景色下的颜色使用。不建议在30%~70%灰度作为背景色的情况下使用品牌标志，该明度与对比度会影响品牌标志的清晰传播。'
            ],
            img: require("../assets/sign/sign_07.png"),
            noboard: true
          }
        ],
        infotwo: [
          {
            title: '品牌架构',
            content: [
              '腾讯广告品牌架构由两级组成，分为广告场景和广告生态两个部分。二级品牌中部分品牌带有独立对外传播的标志与名称，部分仅有名称无独立标志。所有二级品牌标志在使用过程中，只能直接使用资源包里独立制作好的文件，不可随意删减修改二级品牌标志中文字的部分。在使用过程中请严格遵循此规则。'
            ],
            img: require("../assets/sign/sign_08.png"),
            noboard: true
          },
          {
            title: '二级品牌标志',
            content: [
              '二级品牌标志与主品牌标志的并置有严格的规范，该组合标志的大小、比例、间距都按一定的规则组成，不可随意更改任何一项。在形式上分独立标志并置与字标并置两种。为了品牌传播一致性，组合标志里的标志都应使用中文标志。字标并置中的字体，应使用品牌字体中的“思源黑体 SourceHanSansCN-Regular”，且大小、字色应与主品牌标志中的字标保持一致，双行字标的文字大小应与并置分割线高度保持一致。'
            ],
            img: require("../assets/sign/sign_09.png"),
            noboard: false
          },
        ]
      }
    }
  }
</script>

<template>
  <div class="main">

    <TsaHeader ref="header" title='品牌资源' index='1' :blueBlock="blueBlock"></TsaHeader>
    <div class="main-body">

      <div ref="titleBlock" id="title" class="titile-block">
        <p class="title">标志</p>
        <div class="title-content">所有关于腾讯广告主品牌标志与二级品牌标志的应用，都请遵循此规范以保持品牌形象的识别度与统一。</div>
      </div>

      <div class="center-body">
        <div class="blue-block"></div>
        <!-- 信息板块 -->
        <div class="info-body">
          <div class="two-link">
            <a>主品牌标志</a>
            <span class="line">|</span>
            <a class="sec-a" v-on:click="goTwo">二级品牌标志</a>
          </div>
          <div class="tsastitle"><div class="hook" id="main">主品牌标志</div></div>
          <TsaInfo :info="info"></TsaInfo>
          <div class="tsastitle" ><div class="hook" id="secondary" ref="secondary">二级品牌标志</div></div>
          <TsaInfo :info="infotwo"></TsaInfo>
        </div>

      </div>

      <!-- 查看pdf板块 -->
      <div class="pdf-block">
				<a class="pdf-part" href="//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/pdf/TSABrandIndentityGuideline_V1.2.pdf" target="_blank">
          <div class="pdf-bgc"></div>
          <i class="icon-pdf"></i>
          <div class="pdf-content">
            <p class="pdf-title">腾讯广告品牌形象指引
            <i class="pdf-iconright"></i>
            </p>
            <p class="pdf-version">Ver 1.2 / 2018.09</p>
          </div>
				</a>
      </div>

      <!-- 回到顶部板块 -->
      <GoTop v-if="goToFixed"></GoTop>
    </div>

    <!-- 底部板块 -->
    <div class="footer">
      <p>腾讯广告 市场公关中心&设计中心</p>
      <p>Copyright © 1998 - 2019Tencent Inc. All Rights Reserved. </p>
      <img class="footer-bgc" src="../assets/footer.png" alt="">
    </div>
  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
