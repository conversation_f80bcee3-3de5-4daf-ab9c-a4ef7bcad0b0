<script>
 import Brand2x from '@/assert/Banner/<EMAIL>'
 import VI_sign_012x from '@/assert/VI/sign/<EMAIL>'
 import VI_sign_022x from '@/assert/VI/sign/<EMAIL>'
  export default {
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        bannerMsg: {
          title: '品牌形象规范',
          content: [
            '所有关于腾讯广告的主品牌与二级品牌的应用，',
            '请遵循此规范以建立专业统一的品牌形象。'
          ],
          pic: Brand2x
        },
        sidebarOptions: [
          {
            name: '主品牌标志',
            link: '/Brandnoc',
          },
          {
            name: '主品牌标志使用规范',
            link: '/Brand/BrandStand',
          },
          {
            name: '品牌色彩',
            link: '/Brand/BrandColor',
          },
          {
            name: '品牌字体',
            link: '/Brand/BrandFont',
          },
          {
            name: '图像',
            link: '/Brand/BrandImg',
          },
          {
            name: '二级品牌标志',
            link: '/Brand/BrandSub',
          },
          {
            name: 'Tencent in 使用规范',
            link: '/Brand/BrandTencentIn',
          },
        ],
        infoHead: {
          title: '主品牌标志',
          content: '关于腾讯广告主品牌的标志，请遵循此规范以保持品牌形象的识别度与统一。'
        },
        info: [
          {
            title: '中文标志',
            content: [
              '腾讯广告中文版标志由两部分元素构成：图形标志、中文字标。标志元素间的大小及位置是固定的，且中文字标不能单独使用。标志只能从规范文件拷贝使用，不能重新绘制或擅自组合。',
              '中文版标志适用于品牌并置或无外语需求的场景，在小面积区域如中文站点的导航侧亦适用。无简繁体之分，优先考虑使用横版中文标志，如遇使用空间不足时采用竖版中文标志。'
            ],
            img: [
              VI_sign_012x,
              VI_sign_022x,
            ],
          },
          {
            title: '英文标志',
            content: [
              '腾讯广告英文版标志由两部分元素构成：图形标志、英文字标。标志元素间的大小及位置是固定的，且英文字标不能单独使用。标志只能从规范文件拷贝使用，不能重新绘制或擅自组合。',
              '英文标志适用于海外品牌推广场景，禁止在中文语境中单独使用。',
            ],
            img: [
              require("../assert/VI/sign/<EMAIL>"),
              require("../assert/VI/sign/<EMAIL>"),
            ],
          },
          {
            title: '中英文双语标志',
            content: [
              '腾讯广告中英文双语标志由两部分元素构成：图形标志、双语字标。标志元素间的大小及位置是固定的，且双语字标不能单独使用。标志只能从规范文件拷贝使用，不能重新绘制或擅自组合。',
              '双语标志仅在品牌形象发布场景配合tagline使用。',
            ],
            img: [
              require("../assert/VI/sign/<EMAIL>"),
              require("../assert/VI/sign/<EMAIL>"),
            ],
          },
          {
            title: '品牌理念',
            content: [
              '“美好连接，智慧增长“是腾讯广告品牌价值和品牌形象的浓缩，用以加强腾讯广告的品牌沟通力，传递腾讯广告的价值。组合文件只能从规范文件中拷贝使用，不能重新绘制或擅自更改。',
            ],
            img: [
              require("../assert/VI/sign/<EMAIL>"),
            ],
          },
        ],
      }
    }
  }
</script>

<template>
  <div class="main">
    <transition name="cartoonblock">
      <div class="cartoon-block" :class="cartoon == true ? 'no-cartoon' : ''">
        <cartoon></cartoon>
      </div>
    </transition>

    <div v-if="isBlack">
      <TsaHeaderBlack ref="header" title='品牌资源' index='0' :blueBlock="blueBlock"></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader ref="header" title='品牌资源' index='0' :blueBlock="blueBlock"></TsaHeader>
    </div>

    <TsaTitle ref="titleBlock" id="title" :title="bannerMsg.title" :content="bannerMsg.content" :pic="bannerMsg.pic"></TsaTitle>

    <div class="main-body">
      <TsaSidebar :options="sidebarOptions" index="0"></TsaSidebar>
      <div class="center-body">
        <!-- <div class="blue-block"></div> -->
        <!-- 信息板块 -->
        <div class="info-body">
          <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
          <TsaInfo :info="info"></TsaInfo>

          <!-- 底部导航 -->
					<TsaInfoNav
					 title-right="主品牌标志使用规范" link-right="/Brand/BrandStand"
					></TsaInfoNav>

        </div>
      </div>

      <!-- 回到顶部板块 -->
      <GoTop v-if="goToFixed"></GoTop>
    </div>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
