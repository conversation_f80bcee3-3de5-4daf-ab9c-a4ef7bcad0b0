<script>
export default {
  methods: {},
  mounted() {
    console.warn("如有疑问，请企业微信biubiucchen");

    if (/micromessenger/.test(navigator.userAgent.toLowerCase()) && !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
      this.noWeixin = false;
    } else {
      this.noWeixin = true;
    }

    if (this.noWeixin) {
      let url = decodeURIComponent(this.$route.query.download);

      window.location.href = url;

      console.log(url);
    }
  },

  data() {
    return {
      noWeixin: true,
    };
  },
};
</script>

<template>
  <div class="main zip">
    <img :src="require('../assets/zip/zip.png')" />
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@import "../styles/tsa";
</style>
