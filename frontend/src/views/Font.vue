<script>

	export default {
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
		data() {
			return {
        blueBlock: false,
        isBlack: false,
				bannerMsg: {
          title: '资源下载',
          content: [
            '欢迎下载腾讯广告官方品牌视觉素材文档。',
            '请遵循规范以保持对外品牌传播的形象统一。'
          ],
          pic: require("../assert/Banner/<EMAIL>")
        },
        sidebarOptions: [
          {
            name: '品牌标志',
            link: '/Download',
          },
          {
            name: '品牌标准字',
            link: '/Download/Font',
          },
          {
            name: 'PPT 模板',
            link: '#',
          },
          {
            name: '图片素材',
            link: '#',
          },
          {
            name: '海报模板',
            link: '#',
          },
        ],
        infoHead: {
          title: '品牌标准字',
          content: '所有关于腾讯广告设计输出物的字体运用，请下载官方文件以保持品牌形象的识别度。'
        },
				info: [
					{
						title: '中文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。思源黑体做为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_01.png"),
						noboard: true
					},
					{
						title: '英文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。Daxline Offc做为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_02.png"),
						noboard: false
					}
				],
				downloadOptions: [
					{
						name: '方正悠黑',
						pic: require("../assert/Down/font/<EMAIL>"),
						link: '//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/TencentSocialAds_Logo.zip',
						wxlink: 'https://e.qq.com/brand/#/Zip?download=//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/TencentSocialAds_Logo.zip',
						desc: [
							'版权使用范围：',
							'1.思源黑体可以不受限制的免费使用',
							'2.采用Apache 2.0许可证授权',
						],
					},
					{
						name: 'Helvetica',
						pic: require("../assert/Down/font/<EMAIL>"),
						link: '//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/TencentSocialAds_Logo.zip',
						wxlink: 'https://e.qq.com/brand/#/Zip?download=//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/TencentSocialAds_Logo.zip',
						desc: [
							'版权使用范围：',
							'仅腾讯广告业务与部门可使用helvecita字体',
						],
					},
					{
						name: '腾讯体',
						pic: require("../assert/Down/font/<EMAIL>"),
						link: '//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/TencentSocialAds_Logo.zip',
						wxlink: 'https://e.qq.com/brand/#/Zip?download=//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/TencentSocialAds_Logo.zip',
						desc: [
							'版权使用范围：',
							'1.腾讯公司所属业务与部门可使用腾讯体',
							'2.腾讯公司投资企业可使用腾讯体',
							'警告：超出权益适用范围，腾讯法务部将追究法律责任',
						],
					},
				],
			}
		}
	}
</script>

<template>
	<div class="main">

		<!-- <TsaHeader ref="header" title='品牌资源' index='1'></TsaHeader> -->
		<div v-if="isBlack">
      <TsaHeaderBlack ref="header" title='品牌资源' index='1' :blueBlock="blueBlock"></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader ref="header" title='品牌资源' index='1' :blueBlock="blueBlock"></TsaHeader>
    </div>

    <TsaTitle :title="bannerMsg.title" :content="bannerMsg.content" :pic="bannerMsg.pic"></TsaTitle>

		<div class="main-body">

      <TsaSidebar :options="sidebarOptions" index="1"></TsaSidebar>

			<div class="center-body">
				<div class="info-body">
          <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
					<!-- 下载板块 -->
					<!-- 如果不是微信展示下方 -->
					<div class="down-body" v-if="!isWeixin">
						<div class="down-block">
							<div class="down-child" v-for="(item ,key) in downloadOptions" :key="key">
								<TsaDown :image="item.pic" :href="item.link" :name="item.name" :desc="item.desc"></TsaDown>
							</div>
						</div>
					</div>

					<!-- 如果是微信展示下方 -->
					<div class="down-body" v-if="isWeixin">
						<p class="down-title">下载主品牌标志</p>
						<div class="down-block">
							<div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
								<TsaDown :image="item.pic" :href="item.wxlink" :name="item.name"></TsaDown>
							</div>
						</div>

						<p class="down-title">字体</p>
						<div class="down-block ">
							<div class="down-child">
								<TsaDown :image='require("../assets/down/down_09.png")' href="https://e.qq.com/brand/#/Zip?download=https://d3g.qq.com/tsa/SourceHanSans.zip" name='方正悠黑'></TsaDown>
								<div class="down-content-block">
									<p>版权使用范围：</p>
									<p>采用Apache 2.0许可证授权</p>
								</div>
							</div>
							<div class="down-child">
								<TsaDown :image='require("../assets/down/down_10.png")' href="https://e.qq.com/brand/#/Zip?download=//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/DaxlineOffc.zip" name='DaxlineOffc'></TsaDown>
								<div class="down-content-block">
									<p>版权使用范围：</p>
									<p>仅腾讯广告业务与部门可使用DaxlineOffc字体 </p>
								</div>
							</div>
							<div class="down-child">
								<TsaDown :image='require("../assets/down/down_11.png")' href="https://e.qq.com/brand/#/Zip?download=//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/down/TTTGB-Medium.zip" name='腾讯字体'></TsaDown>
								<div class="down-content-block">
									<p>版权使用范围：</p>
									<p>1.腾讯公司所属业务与部门可使用腾讯体 </p>
									<p>2.腾讯公司投资企业可使用腾讯体</p>
									<p>警告：若超出权益适用范围，腾讯法务部可追究法律责任</p>
								</div>
							</div>
						</div>
						<p class="down-title">辅助图形</p>
						<div class="down-block">
							<div class="down-child">
								<TsaDown :image='require("../assets/down/down_12.png")' href="https://e.qq.com/brand/#/Zip?download=https://d3g.qq.com/tsa/TSA_illustration.zip" name='辅助图形'></TsaDown>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 回到顶部板块 -->
			<GoTop v-if="goToFixed"></GoTop>
		</div>

		<!-- 底部板块 -->
    <TsaFooter></TsaFooter>
	</div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
