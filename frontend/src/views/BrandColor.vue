<script>

  export default {
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '品牌色彩',
          content: '所有关于腾讯广告设计输出物的色彩运用，都请遵循此规范以保持品牌形象的识别度与统一。'
        },
        info: [
          {
            title: '核心色板',
            content: [
              '品牌核心色板在品牌视觉传达中起到核心左右。每个品牌色都有精准匹配合适的Pantone色彩，对于平面及广告印刷品，一般情况下须选择对应的Pantone色值；若因条件限制无法使用Pantone色彩，选取对应的CMYK色值作为第二选择。',
              '准对界面、网页、PowerPoint演示文稿等显示屏色彩，须使用RGB色值或Hex值。'
            ],
            img: [
              require("../assert/VI/color/VI_color_01.png"),
              require("../assert/VI/color/VI_color_02.png"),
              require("../assert/VI/color/VI_color_03.png"),
              require("../assert/VI/color/VI_color_04.png"),
              require("../assert/VI/color/VI_color_05.png"),
              require("../assert/VI/color/VI_color_06.png"),
            ],
          },
          {
            title: '辅助色板',
            content: [
              '当品牌核心色板无法满足复杂的设计需求时，可以使用辅助色板里的颜色进行设计。',
              '辅助色板里的每项色彩均由核心色板里的品牌色通过一定的递进数据调整得出，其中递进的步数为5，在色相不变的前提下每步为10 的明度差微调饱和度。如需要更细化的颜色，可依据此规律去调整出所需颜色。',
            ],
            img: [
              require("../assert/VI/color/VI_color_07.png"),
              require("../assert/VI/color/VI_color_08.png"),
              require("../assert/VI/color/VI_color_09.png"),
              require("../assert/VI/color/VI_color_10.png"),
              require("../assert/VI/color/VI_color_11.png"),
              require("../assert/VI/color/VI_color_12.png"),
              require("../assert/VI/color/VI_color_12_1.png"),
              require("../assert/VI/color/VI_color_12_2.png"),
            ],
          },
          {
            title: '色环',
            content: [
              '恰当的颜色比例使用可确保用户正确认知腾讯广告的品牌，为保证设计与品牌间的关联性，运用色彩应遵循一定的品牌色传承。',
              '其中大面积使用色或背景色应为白色与深蓝，点缀色可采用腾讯广告品牌标志色。',
            ],
            img: [
              require("../assert/VI/color/<EMAIL>"),
            ],
          },
          {
            title: '市场营销使用场景',
            content: [
                    '主要适用于H5、公众号配图、插图场景、图标等通过不同颜色组合及使用比例，以及传递更丰富的具有视觉表现力的图形信息。请遵循此规范以保持品牌形象的识别度统一。'
            ],
            img: [
                    require('../assert/VI/color/VI_color_14.png'),
                    require('../assert/VI/color/VI_color_15.png'),
                    require('../assert/VI/color/VI_color_16.png'),
                    require('../assert/VI/color/VI_color_17.png'),
                    require('../assert/VI/color/VI_color_18.png'),
                    require('../assert/VI/color/VI_color_19.png'),
            ]
          },
          {
            title: '线上终端应用场景',
            content: [
              '主要适用于小程序、各种子品牌官网，通过不同颜色区分功能、层级以及传递更丰富的具有视觉表现力的功能信息，请遵循此规范以保持品牌形象的识别度统一。'
            ],
            img: [
              require('../assert/VI/color/VI_color_20.png'),
              require('../assert/VI/color/VI_color_21.png'),
              require('../assert/VI/color/VI_color_22.png'),
              require('../assert/VI/color/VI_color_23.png'),
              require('../assert/VI/color/VI_color_24.png'),
              require('../assert/VI/color/VI_color_25.png'),
              require('../assert/VI/color/VI_color_26.png'),
            ]
          }
        ],
      }
    }
  }
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <TsaInfo :info="info"></TsaInfo>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
</style>
