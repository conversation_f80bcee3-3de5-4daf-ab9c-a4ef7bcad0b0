<script>

	export default {
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
		data() {
			return {
        blueBlock: false,
        isBlack: false,
				info: [
					{
						title: '核心色板',
						content: [
							'品牌核心色板在品牌视觉传达中起到了核心作用。右图所示为品牌规范核心色板示意,这些颜色可以运用在任何品牌有关衍生物料中。每一个品牌色都有着精准匹配合适的 Pantone 色彩，关于平面与广告印刷物,一般情况使用对应的 Pantone 色值;若因条件限制无法使用 Pantone 色彩,对应的 CMYK 色值做为第二选择。关于界面、网页、PowerPoint演示文稿等显示器颜色，须使用RGB色值或十六进制值。'
						],
						img: require("../assets/color/color_01.png"),
						noboard: true
					},
					{
						title: '辅助色板',
						content: [
							'当品牌核心色板无法满足复杂的设计需求时，可以使用辅助色板里的颜色进行设计。辅助色板里的每项色彩均由核心色板里的品牌色通过一定的递进数据调整得出，其中递进的步数为5，在色相不变的前提下每步为20的明度差再微调饱和度。如需要更细化的颜色，可依据此规律去调整出所需颜色。'
						],
						img: require("../assets/color/color_02.png"),
						noboard: true
					},
					{
						title: '色环',
						content: [
							'恰当的颜色比例使用可确保所有用户正确认知腾讯广告的品牌。其中大面积使用色或背景色应为白色与深蓝；设计点缀色可使用腾讯广告品牌四色(红黄蓝绿)，使用时注意四色应同时出现，不能单独使用其中一色，且在颜色排序上，应保持红、黄、蓝、绿这样的顺序，尽量避免调换四色顺序；灰色也可作为设计点缀色进行使用。'
						],
						img: require("../assets/color/color_03.png"),
						noboard: false
					}
				]
			}
		}
	}
</script>

<template>
	<div class="main">

		<TsaHeader ref="header" title='品牌资源' index='2' :blueBlock="blueBlock"></TsaHeader>
		<div class="main-body">

			<div ref="titleBlock" id="title" class="titile-block">
				<p class="title">色彩</p>
				<div class="title-content">所有关于腾讯广告设计输出物的色彩运用，<br/>都请遵循此规范以保持品牌形象的识别度与统一。</div>
			</div>

			<div class="center-body">
				<div class="blue-block"></div>
				<!-- 信息板块 -->
				<div class="info-body">
					<TsaInfo :info="info"></TsaInfo>
				</div>

			</div>

			<!-- 查看pdf板块 -->
			<div class="pdf-block">
				<a class="pdf-part" href="//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/pdf/TSABrandIndentityGuideline_V1.2.pdf" target="_blank">
					<div class="pdf-bgc"></div>
					<i class="icon-pdf"></i>
					<div class="pdf-content">
						<p class="pdf-title">腾讯广告品牌形象指引
						<i class="pdf-iconright"></i>
						</p>
						<p class="pdf-version">Ver 1.2 / 2018.09</p>
					</div>
				</a>
			</div>

			<!-- 回到顶部板块 -->
			<GoTop v-if="goToFixed"></GoTop>
		</div>

		<!-- 底部板块 -->
		<div class="footer">
			<p>腾讯广告 市场公关中心&设计中心</p>
			<p>Copyright © 1998 - 2019Tencent Inc. All Rights Reserved. </p>
			<img class="footer-bgc" src="../assets/footer.png" alt="">
		</div>
	</div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
