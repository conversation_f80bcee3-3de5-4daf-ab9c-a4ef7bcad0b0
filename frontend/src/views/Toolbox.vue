<script>
export default {
  created() {
    this.watchScroll();
  },
  beforeDestroy() {
    this.unWatchScroll();
  },
  data() {
    return {
      blueBlock: false,
      isBlack: false,
      bannerMsg: {
        title: "工具箱",
        content: [
          "所有关于腾讯广告的对外沟通的应用工具，",
          "请采用统一的工具以建立专业的品牌形象。",
        ],
        pic: require("../assert/Banner/<EMAIL>"),
      },
      sidebarOptions: [
        {
          name: "主品牌标志",
          link: "#",
        },
        {
          name: "主品牌标志应用",
          link: "#",
        },
        {
          name: "品牌色彩",
          link: "#",
        },
      ],
      cardInfo: [
        {
          pic: require("../assert/Tool/<EMAIL>"),
          options: {
            title: "部门名片",
            content: [
              "名片是大部分同事商业合作的重要工具，有需要的同事请前往adm.oa.com，进入“个人物资”申请名片。",
              "*特别提醒：英文名形式之间留一个空格。正确示范：Lily Li; 错误样例： Lilyli。",
            ],
            button: {
              value: "前往申请",
              link: "http://work.oa.com/applyfill.aspx?flowid=100",
              disabled: false,
            },
          },
        },
        {
          pic: require("../assert/Tool/<EMAIL>"),
          options: {
            title: "邮件签名工具",
            content: [
              "邮件是大部分同事对外沟通的重要渠道，一封严谨、专业、美观的邮件，可让收件人对腾讯广告产生可信赖与良好的印象。",
              "AMS小伙伴可通过邮件签名工具，自助填写资料，即可生成一张邮件签名图片。",
            ],
            // button: { value: '服务器调整中，请联系 Biubiucchen', link: 'http://ad.oa.com/mailsignature/', disabled: true }
            button: { value: "立即生成", link: "#/Mail", disabled: false },
          },
        },
      ],
    };
  },
};
</script>

<template>
  <div class="main">
    <div v-if="isBlack">
      <TsaHeaderBlack
        ref="header"
        title="品牌资源"
        index="3"
        :blueBlock="blueBlock"
      ></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader
        ref="header"
        title="品牌资源"
        index="3"
        :blueBlock="blueBlock"
      ></TsaHeader>
    </div>

    <TsaTitle
      ref="titleBlock"
      :title="bannerMsg.title"
      :content="bannerMsg.content"
      :pic="bannerMsg.pic"
    ></TsaTitle>

    <div>
      <div
        class="card-container"
        v-for="(item, index) in cardInfo"
        :key="index"
      >
        <TsaCard :pic="item.pic" :options="item.options"></TsaCard>
      </div>
    </div>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>

    <!-- 回到顶部板块 -->
    <GoTop v-if="goToFixed"></GoTop>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@import "../styles/tsa";

@keyframes BannerOpacity1 {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

.card-container {
  margin: 0 auto;
  margin-top: 48px;
  max-width: 1200px;
  border-radius: 12px;
  border: 1px solid #e0eaff;
  background-color: #fff;
  opacity: 0;
  animation: BannerOpacity1 1s ease forwards;

  @media screen and (max-width: $screen-lg) {
    max-width: 984px;
  }
  &:first-child {
    margin-top: 64px;
  }
}
</style>
