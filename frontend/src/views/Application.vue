<script>
 import Support2x from '@/assert/Banner/<EMAIL>'
 import AdLogo from '@/assets/ad_logo.png'
  export default {
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
    data() {
      return {
        blueBlock: false,
        isBlack: false,
        bannerMsg: {
          title: '服务商应用指南',
          content: [
            '所有关于腾讯广告服务商的品牌形象应用，',
            '请遵循此规范以保持对外品牌传播的形象统一。'
          ],
          pic: Support2x
        },
        cardInfo: [
          {
            pic: AdLogo,
            options: {
              title: '腾讯授权(北京)区域营销服务中心',
              button: { value: '打开PDF', link: 'https://down.qq.com/tad/Tencent_authorization_of_Beijing_marketing_service.pdf' }
            },
          },
          {
            pic: AdLogo,
            options: {
              title: '腾讯授权(上海)区域营销服务中心',
              button: { value: '打开PDF', link: 'https://down.qq.com/tad/Tencent_authorization_of_Shanghai_marketing_service.pdf' }
            },
          },
        ]
      }
    }
  }
</script>

<template>
  <div class="main">

    <div v-if="isBlack">
      <TsaHeaderBlack ref="header" title='品牌资源' index='2' :blueBlock="blueBlock"></TsaHeaderBlack>
    </div>
    <div v-else>
      <TsaHeader ref="header" title='品牌资源' index='2' :blueBlock="blueBlock"></TsaHeader>
    </div>

    <TsaTitle ref="titleBlock" id="title" :title="bannerMsg.title" :content="bannerMsg.content" :pic="bannerMsg.pic"></TsaTitle>

    <div class="small-card-body">
      <div class="card-container" v-for="(item, index) in cardInfo" :key="index">
        <TsaSmallCard :pic="item.pic" :options="item.options"></TsaSmallCard>
      </div>
    </div>

    <!-- 底部板块 -->
    <TsaFooter></TsaFooter>

    <!-- 回到顶部板块 -->
    <GoTop v-if="goToFixed"></GoTop>
  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
  @import "../styles/tsa";
  @keyframes BannerOpacity1 {
		0% {
				opacity: 0;
				transform: translateY(20px);
		}

		100% {
				opacity: 1;
				transform: translateY(0px);
		}
	}
 .small-card-body {
    margin: 64px auto auto auto;
    /*margin-top: 64px;*/
    max-width: 1200px;
    .card-container {
      display: inline-block;
      width: 380px;
      margin: 0 30px 0 0;
      overflow: hidden;
      background-image: url('../assert/Public/pr_nbgc.png');
      background-size: 382px auto;
      background-repeat: no-repeat;
      background-origin: border-box;
      border: 2px solid #E0EAFF;
      box-shadow: 0 4px 20px 0 rgba(0,116,255,0.08);
      border-radius: 12px;
      transition: all .2s;
              opacity: 0;
   animation: BannerOpacity1 1s ease forwards;

      &:hover {
        border: 2px solid #296BEF;
        box-shadow: 0 1px 28px 0 rgba(44,114,255,0.1),
        0 4px 20px 0 rgba(0,116,255,0.08);
      }

      &:nth-child(n + 4) {
        margin-top: 30px;
      }
      &:nth-child(3n) {
        margin-right: 0;
      }
    }

    @media (max-width: $screen-lg) {
      max-width: 984px;

      .card-container {
          margin: 0 24px 0 0;
          width: 312px;
          background-size: 316px auto;

          &:nth-child(n + 4) {
            margin-top: 24px;
          }
        }
			}
  }
</style>
