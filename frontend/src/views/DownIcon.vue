<script>

	export default {
		data() {
			return {
        blueBlock: false,
        isBlack: false,
        infoHead: {
          title: '图标素材',
          content: '使用腾讯广告的品牌图标素材以保持品牌形象的识别度与统一，请勿自行编辑或更改任意元素。'
        },
				info: [
					{
						title: '中文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。思源黑体做为腾讯广告的中文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_01.png"),
						noboard: true
					},
					{
						title: '英文',
						content: [
							'品牌专用字体是品牌视觉识别的重要部分。品牌专用字体风格有助于建立品牌独特的美感。Daxline Offc做为腾讯广告的英文品牌专用字体，被用于品牌产品及品牌视觉传达中。我们需要遵循右页所述的品牌字体运用示例，以确保品牌视觉传达的一致性。'
						],
						img: require("../assets/font/font_02.png"),
						noboard: false
					}
				],
				downloadOptions: [
					{
						name: '图标素材',
						pic: require("../assert/Down/icon/Down_icon_01.png"),
						link: 'https://down.qq.com/tad/AMS_Icon.zip',
					},
				],
			}
		}
	}
</script>

<template>
  <div class="info-body">
    <TsaInfoHead :infohead="infoHead"></TsaInfoHead>
    <!-- 下载板块 -->
    <!-- 如果不是微信展示下方 -->
    <div class="down-body" v-if="!isWeixin">
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.link" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 如果是微信展示下方 -->
    <div class="down-body" v-if="isWeixin">
      <p class="down-title">下载主品牌标志</p>
      <div class="down-block">
        <div class="down-child" v-for="(item, key) in downloadOptions" :key="key">
          <TsaDown :image="item.pic" :href="item.wxlink" :name="item.name"></TsaDown>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <!-- <TsaInfoNav
     title-left="图片" link-left="/Download/DownImg"
     title-right="海报模板" link-right="/Download/DownPoster"
    ></TsaInfoNav> -->

  </div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
