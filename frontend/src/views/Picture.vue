<script>

	export default {
    created() {
      this.watchScroll()

    },
    beforeDestroy() {
      this.unWatchScroll()
    },
		data() {
			return {
        blueBlock: false,
        isBlack: false,
				info: [
					{
						title: '影像关键词',
						content: [
							'腾讯广告的品牌影像关键词为商业、人、技术、连接。通过具有固定风格与内容的影像来传递品牌核心信息，能增强品牌在用户心中的视觉形象。使用具有商务元素的场景来传达品牌的商业气质；使用多人欢乐的沟通场景来寓意广告的精确触达；使用带有编程、代码元素的场景来表现品牌务实的技术支撑；通过带有连接、传达含义的图像来表现广告传达的品牌本质。'
						],
						img: require("../assets/picture/picture_01.png"),
						noboard: true
					},
					{
						title: '影像风格建议',
						content: [
							'在品牌影像风格上，应紧扣交流沟通、商务合作、数据指标、数据技术、城市航拍、智能抽象等风格关键词进行应用。在选择品牌影像时，应注意该影像内容与当前设计物内容的契合度，避免盲目追求视觉效果而使用内容契合度不高的影像进行使用。如需要表现品牌广阔覆盖力与影响力时，可选择城市航拍的影像进行使用，且需要注意影像中的城市需选择腾讯广告的根据地深圳或北京，加强影像与内容的关联性。'
						],
						img: require("../assets/picture/picture_02.png"),
						noboard: true
					},
					{
						title: '错误使用示范',
						content: [
							'为了保证品牌视觉形象的正向传达，在影像的拍摄与选择上需要保证内容、光线、造型、场景等所有元素都是正确。避免在使用的影像中出现右图的错误示范。'
						],
						img: require("../assets/picture/picture_03.png"),
						noboard: false
					}
				]
			}
		}
	}
</script>

<template>
	<div class="main">

		<TsaHeader ref="header" title='品牌资源' index='4' :blueBlock="blueBlock"></TsaHeader>
		<div class="main-body">

			<div ref="titleBlock" id="title" class="titile-block">
				<p class="title">图像</p>
				<div class="title-content">所有关于腾讯广告对外输出物中图片和影像的运用，<br/>都请遵循此规范以保持品牌形象的识别度与统一。</div>
			</div>

			<div class="center-body">
				<div class="blue-block"></div>
				<!-- 信息板块 -->
				<div class="info-body">
					<TsaInfo :info="info"></TsaInfo>
				</div>

			</div>

			<!-- 查看pdf板块 -->
			<div class="pdf-block">
				<a class="pdf-part" href="//i.gtimg.cn/qzone/biz/gdt/eqqcom/brand/static/pdf/TSABrandIndentityGuideline_V1.2.pdf" target="_blank">
					<div class="pdf-bgc"></div>
					<i class="icon-pdf"></i>
					<div class="pdf-content">
						<p class="pdf-title">腾讯广告品牌形象指引
						<i class="pdf-iconright"></i>
						</p>
						<p class="pdf-version">Ver 1.2 / 2018.09</p>
					</div>
				</a>
			</div>

			<!-- 回到顶部板块 -->
			<GoTop v-if="goToFixed"></GoTop>
		</div>

		<!-- 底部板块 -->
		<div class="footer">
			<p>腾讯广告 市场公关中心&设计中心</p>
			<p>Copyright © 1998 - 2019Tencent Inc. All Rights Reserved. </p>
			<img class="footer-bgc" src="../assets/footer.png" alt="">
		</div>
	</div>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	@import "../styles/tsa";
</style>
