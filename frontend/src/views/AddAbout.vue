<style scope>
.content {
  padding: 56px 0 100px 0;
  color: #444;
  line-height: 24px;
  font-size: 16px;
}
.content h3 {
  color: #000;
  font-size: 20px;
  margin-bottom: 16px;
  font-weight: normal;
}
.content h3:not(:first-child) {
  margin-top: 56px;
}
.content img {
  margin-top: 40px;
}
p {
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0.5px;
}
.content body,
.content html {
  background: transparent;
}
</style>

<template>
  <div>
    <BoxHead style="">
      <div slot="title"><span style="">Q & A</span></div>
      <div slot="right"></div>
    </BoxHead>
    <div class="content" style="margin-left: 52px; margin-right: 52px">
      <markdown />
    </div>
  </div>
</template>

<script>
import Markdown from "@/mark/About.md";
import BoxHead from "@/components/BoxHead.vue";

export default {
  components: {
    BoxHead,
    Markdown,
  },
  data() {
    return {};
  },
};
</script>
