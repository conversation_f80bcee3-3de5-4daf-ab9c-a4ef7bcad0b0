/**
 * @version 1.0
 */

const loginRtx = ({successCb, backUrl}) => {
    const errorCb = () => {
        let loginTimes = (Number(localStorage.getItem('redirect_to_login')) || 0) + 1
        if(loginTimes > 3) {
            setTimeout(() => alert('跳转失败，请自行登录oa'), 0)
            return false
        }
        localStorage.setItem('redirect_to_login', loginTimes)
        // eslint-disable-next-line no-irregular-whitespace
        window.location.href = 'http://passport.oa.com/modules/passport/signin.ashx?url=' +　encodeURIComponent(backUrl)
    }
    const url = 'http://logic.ad.mmbiz.oa.com/cgi-bin/privilege/privilegemgr?action=whoami'

    $.ajax({
        dataType: 'jsonp',
        url,
        success: (resp) => {
            console.log(resp)
            /*jQuery31106205707665148408_1510805605970({"data":{"chinese_name":"肖建锋","department_name":"社交与效果广告部","english_name":"yexiao","full_name":"yexia<PERSON>(肖建锋)","gender":"男","group_name":"微信广告设计组","login_name":"yexiao","official_name":"普通员工","rtx":"yexiao"},"msg":"ok","ret":0}
            );*/
            if(!resp || !resp.data) {
                errorCb()
            }
            else {
                localStorage.removeItem('redirect_to_login')
                successCb(resp.data)
            }
        },
        error: errorCb
    })
}

export default loginRtx