export default {
  data() {
    return {
      firstIn: 0,
      isMoible: false,
      cartoon: false,
      goToFixed: false,
      isWeixin: false,
    };
  },
  methods: {
    // 内容区置顶蓝色方块
    infoBlock() {
      let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      if (scrollTop) {
        this.cartoon = true;
        if (this.firstIn === 0) {
          this.firstGoTop();
        }
        this.firstIn++;
      }
      let titleHeight = this.$refs.titleBlock.$el.offsetHeight; //获取标题板块高度
      let headerHeight = this.$refs.header.$el.offsetHeight; //获取头部组件高度
      if (this.isMoible) {
        headerHeight = headerHeight - 63; //如果是移动端再减去头部LOGO的高度
      }
      let bluemove = titleHeight - headerHeight; //获取标题板块碰到头部组件的高度
      this.blueBlock = scrollTop >= bluemove;
      this.isBlack = scrollTop >= bluemove;
      this.goToFixed = scrollTop >= bluemove;
    },
    // 第一页动画
    firstPage() {
      let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      if (scrollTop) {
        this.cartoon = true;

        if (this.firstIn === 0) {
          // document.body.style.overflow='hidden';
          this.firstGoTop();
        }

        this.firstIn++;
        //console.log(this.firstIn)
      }
    },

    firstGoTop() {
      let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      const mo = function(e) {
        e.preventDefault();
      };
      setTimeout(function() {
        document.body.style.overflow = "inherit";
      }, 1000);
    },

    watchScroll() {
      console.warn("如有疑问，请企业微信biubiucchen");
      let m_width = document.body.clientWidth;
      let u = navigator.userAgent;

      if (m_width <= 960) {
        this.isMoible = true;
      }

      if (/micromessenger/.test(navigator.userAgent.toLowerCase()) && !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
        this.isWeixin = true;
      }

      // window.addEventListener('scroll', this.firstPage);
      window.addEventListener("scroll", this.infoBlock);
    },
    unWatchScroll() {
      window.removeEventListener("scroll", this.firstPage);
      window.removeEventListener("scroll", this.infoBlock);
    },
  },
};
