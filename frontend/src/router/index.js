import Vue from 'vue'
import Router from 'vue-router'
import Path from './path'


Vue.use(Router)
const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

export default new Router({
  mode: "hash",
  routes: [
    // brand Router
    {
      path: '/',
      component: Path.BrandIndex,
      redirect: '/Brand',
      children: [
        {
          path: '/Brand', name: 'BrandIntroduce', component: Path.BrandIntroduce, meta: { index: 0 }
        },
        {
          path: '/Brand/Brandnoc', name: 'Brandnoc', component: Path.Brandnoc, meta: { index: 1 }
        },
        {
          path: '/Brand/BrandStand', name: 'BrandStand', component: Path.BrandStand, meta: { index: 2 }
        },
        {
          path: '/Brand/BrandColor', name: 'BrandColor', component: Path.BrandColor, meta: { index: 3 }
        },
        {
          path: '/Brand/BrandFont', name: 'BrandFont', component: Path.BrandFont, meta: { index: 4 }
        },
        {
          path: '/Brand/BrandImg', name: 'BrandImg', component: Path.BrandImg, meta: { index: 5 }
        },
        {
          path: '/Brand/BrandIllustration', name: 'BrandIllustration', component: Path.BrandIllustration, meta: { index: 6 }
        },
        {
          path: '/Brand/BrandIcon', name: 'BrandIcon', component: Path.BrandIcon, meta: { index: 7 }
        },
        {
          path: '/Brand/BrandSub', name: 'BrandSub', component: Path.BrandSub, meta: { index: 8 }
        },
        {
          path: '/Brand/BrandTencentIn', name: 'BrandTencentIn', component: Path.BrandTencentIn, meta: { index: 9 }
        },
      ]
    },
    {
      path: '/Sign', name: 'Sign', component: Path.Sign,
    },
    {
      path: '/Color', name: 'Color', component: Path.Color,
    },
    {
      path: '/Picture', name: 'Picture', component: Path.Picture,
    },
    // Down Router
    {
      path: '/',
      component: Path.DownIndex,
      redirect: '/Download',
      children: [
        {
          path: '/Download', name: 'DownStandard', component: Path.DownStandard, meta: { index: 0 }
        },
        {
          path: '/Download/sign', name: 'Download', component: Path.Download, meta: { index: 1 }
        },
        {
          path: '/Download/DownFont', name: 'DownFont', component: Path.DownFont, meta: { index: 2 }
        },
        {
          path: '/Download/DownPPT', name: 'DownPPT', component: Path.DownPPT, meta: { index: 3 }
        },
        {
          path: '/Download/DownImg', name: 'DownImg', component: Path.DownImg, meta: { index: 4 }
        },
        {
          path: '/Download/DownIcon', name: 'DownIcon', component: Path.DownIcon, meta: { index: 5 }
        },
        {
          path: '/Download/DownPoster', name: 'DownPoster', component: Path.DownPoster, meta: { index: 6 }
        },
      ]
    },
    {
      path: '/Zip', name: 'Zip', component: Path.Zip,
    },
    {
      path: '/Toolbox', name: 'Toolbox', component: Path.Toolbox,
    },
    {
      path: '/PublicRelations', name: 'PublicRelations', component: Path.PublicRelations,
    },
    {
      path: '/Application', name: 'Application', component: Path.Application,
    },
    {
      path: '/BrandApplication', name: 'BrandApplication', component: Path.BrandApplication,
    },
    {
      path: '/BrandIntro', name: 'BrandIntro', component: Path.BrandIntro,
    },
    {
      path: '/Mail',
      component: Path.Parent,
      children: [
        {
          name: 'maker-add',
          path: '/Mail',
          component: Path.Add
        },
        {
          name: 'maker-about',
          path: '/MailAbout',
          component: Path.AddAbout
        },
        {
          name: 'maker-demo',
          path: '/MailDemo',
          component: Path.AddDemo
        }
      ]
    }
  ],
  // scrollBehavior (to, from, savedPosition) {
  //   if (to.hash) {
  //     return {
  //       selector: to.hash
  //     }
  //   } else {
  //     return { x: 0, y: 0 }
  //   }
  // },

})
