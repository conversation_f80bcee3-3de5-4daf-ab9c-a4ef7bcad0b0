@use "../styles/default" as *;

.icon {
  display: inline-block;
  background-image: url("../assets/tsa_sprite.png");
  background-size: 102px auto;
}

* {
  margin: 0;
  padding: 0;
}

@font-face {
  font-family: "Tsans";
  src: url("../assert/Font/TencentSans-W7/webfonts/TencentSans-W7.woff")
      format("woff"),
    url("../assert/Font/TencentSans-W7/webfonts/TencentSans-W7.eot"),
    url("../assert/Font/TencentSans-W7/ot_ttf/TencentSans-W7.ttf")
      format("truetype");
  font-weight: normal;
  font-style: normal;
}

.qicon {
  display: inline-block;
  background-image: url("../assets/qsprite.png");
  background-size: 36px auto;
}

.titile-block {
  background-color: $primary;
  height: 560px;
  padding: 184px 0 72px;
  color: #fff;
  text-align: center;
  .title {
    font-size: 48px;
  }
  .title-content {
    margin-top: 20px;
    display: inline-block;
    width: 450px;
    line-height: 1.5;
    opacity: 0.5;
    font-size: 16px;
    color: #ffffff;
    font-weight: normal;
    letter-spacing: 1px;
  }
  @media (max-width: $screen-md) {
    padding: 174px 0 50px;
    .title {
      font-size: 36px;
    }
    .title-content {
      padding: 0 25px;
      width: auto;
      font-size: 12px;
    }
  }
}

.main {
  background-color: $body-bg-color;
  flex-direction: column;
  display: flex;
}

@keyframes BLockOpacity {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

.main-body {
  margin: 0 auto;
  margin-top: 64px;
  max-width: 1200px;
  border-radius: 12px;
  border: 1px solid #e0eaff;
  display: flex;
  justify-content: flex-start;
  background-color: #fff;

  @media screen and (max-width: $screen-lg) {
    max-width: 984px;
  }
  .info-body {
    position: relative;
    padding: 54px 116px 48px 116px;
    @media (max-width: $screen-lg) {
      padding: 54px 64px 44px 64px;
    }
  }
  .down-body {
    position: relative;
    margin-top: 64px;
    .down-title {
      margin-bottom: 36px;
      font-size: 24px;
      color: #0f1f3d;
      font-weight: bold;
    }
    @media (max-width: $screen-md) {
      .down-title {
        font-size: 18px;
        padding-top: 32px;
        margin-bottom: 20px;
      }
    }
  }
  .center-body {
    overflow-x: hidden;
    //position: relative;
    box-sizing: content-box;
    border-left: 2px solid $guide-line-color;
    flex: 1;
    width: 954px;
  }
}

.blue-block {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #0099ff;
  width: 100%;
  height: 8px;
  z-index: 1;
}

// 下载板块
.down-block {
  // padding-bottom: 48px;
  // font-size: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.down-border {
  border-bottom: 1px solid #e1e4e6;
}

.down-child {
  display: inline-block;
  margin-bottom: 40px;
  .down-content-block {
    margin-top: -14px;
    display: block;
    color: rgba($color: #0f1f3c, $alpha: 0.5);
    font-size: 12px;
    p {
      text-align: left;
      margin-bottom: 4px;
      width: 328px;
      line-height: 16px;
      letter-spacing: 0.5px;
    }
  }
  @media (max-width: 1150px) {
    width: 46%;
    margin-bottom: 28px;
    &:nth-last-child(1) {
      margin-bottom: 0;
    }
    .down-content-block {
      p {
        width: 100%;
      }
    }
  }
  @media (max-width: $screen-md) {
    &:nth-last-child(1) {
      margin-bottom: 0;
    }

    .down-content-block {
      margin-top: 10px;
    }
  }
}

// 查看pdf板块
.pdf-block {
  padding-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  a {
    position: relative;
    padding: 18px 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #0099ff;
    transition: background-color 0.8s;
    background: linear-gradient(to left, #0099ff, #008de8);
    .pdf-bgc {
      position: absolute;
      top: 0;
      left: 0;
      display: inline-block;
      width: 100%;
      height: 100%;
      background-color: #0f1e3c;
      z-index: 0;
      transition: opacity 0.8s;
    }
    .icon-pdf {
      width: 32px;
      height: 40px;
      display: inline-block;
      background-image: url("../assets/pdf_logo.png");
      background-size: 32px auto;
      background-repeat: no-repeat;
      z-index: 1;
    }
    .pdf-content {
      margin-left: 18px;
      z-index: 1;
      text-align: left;
      .pdf-title {
        margin-bottom: 8px;
        font-size: 16px;
        color: #00a2ff;
      }
      .pdf-version {
        font-size: 12px;
        color: rgba($color: #00a2ff, $alpha: 0.5);
      }
    }
    .pdf-iconright {
      @extend .icon;
      z-index: 1;
      margin-left: 16px;
      height: 12px;
      width: 22px;
      background-position: -80px 0;
    }
    &:hover {
      transition: all 0.8s;
      .icon-pdf {
        transition: all 0.8s;
        background-image: url("../assets/pdf_logohover.png");
      }
      .pdf-iconright {
        background-position: -40px -40px;
      }
      .pdf-content {
        .pdf-title {
          transition: all 0.8s;
          color: #fff;
        }
        .pdf-version {
          transition: all 0.8s;
          color: rgba($color: #fff, $alpha: 0.5);
        }
      }
      .pdf-bgc {
        opacity: 0;
      }
    }
  }
  @media (max-width: $screen-md) {
    padding: 28px 12px 0;
    a {
      width: 100%;
      .pdf-content {
        margin-left: 12px;
        .pdf-title {
          margin-bottom: 6px;
          font-size: 16px;
        }
        .pdf-version {
          font-size: 12px;
        }
      }
      .pdf-iconright {
        margin-left: 6px;
      }
    }
  }
}

// 上下页面板块
.go-block {
  margin: 40px 0;
  display: flex;
  justify-content: space-between;
  .go-part {
    width: 50%;
    padding: 0 16px;
    &:nth-child(odd) {
      text-align: left;
    }
    &:nth-child(even) {
      text-align: right;
    }
  }
  .link {
    display: inline-block;
    color: #00a2ff;
    font-size: 16px;
    .content-left,
    .content-right {
      position: relative;
      left: 0;
      transition: all 0.5s;
      span {
        opacity: 0.7;
      }
    }
    .go-iconleft {
      @extend .icon;
      height: 12px;
      width: 24px;
      opacity: 0.5;
      background-position: -63px -40px;
    }
    .go-iconright {
      @extend .icon;
      height: 12px;
      width: 24px;
      opacity: 0.5;
      background-position: -40px -52px;
    }
    &:hover {
      .content-left {
        left: -16px;
        .go-iconleft,
        span {
          opacity: 1;
        }
      }
      .content-right {
        left: 16px;
        .go-iconright,
        span {
          opacity: 1;
        }
      }
    }
  }
}

// 底部板块
// .footer {
//     margin-top: 72px;
//     font-size: 0;
//     text-align: center;
//     color: rgba($color: #fff, $alpha: 0.5);
//     color: $sub-text-color;
//     p {
//         font-size: 14px;
//         margin-bottom: 40px;
//         &:first-child {
//             margin-bottom: 16px;
//         }
//     }
//     .footer-bgc {
//         height: 8px;
//         width: 100%;
//     }
//     @media (max-width: $screen-md) {
//         p {
//             font-size: 12px;
//             margin-bottom: 15px;
//         }
//     }
// }

// @keyframes qcodehover {
//     0% {
//         transform: scale(0) translate3d(-66.5%, -67%, 0);
//     }
//     100% {
//         transform: scale(1) translate3d(-66.5%, -67%, 0);
//     }
// }

// .go-top {
//     position: fixed;
//     right: 24px;
//     width: 40px;
//     bottom: 80px;
//     .go-topblock {
//         position: relative;
//         height: 40px;
//         .go-icontop {
//             @extend .icon;
//             position: absolute;
//             top: 0;
//             left: 0;
//             height: 40px;
//             width: 40px;
//             background-position: 0 0;
//             cursor: pointer;
//             z-index: 1;
//             transition: opacity .8s;
//         }
//         .go-icontophover {
//             @extend .icon;
//             position: absolute;
//             top: 0;
//             left: 0;
//             height: 40px;
//             width: 40px;
//             background-position: -40px 0;
//             cursor: pointer;
//             z-index: 0;
//         }
//         &:hover {
//             .go-icontop {
//                 opacity: 0;
//             }
//         }
//     }
//     .qrblock {
//         position: relative;
//         .qrbody {
//             position: absolute;
//             left: 0%;
//             top: 0%;
//             transform: scale(1) translate3d(-66.5%, -67%, 0);
//             transform-origin: 30% 30%;
//             transition: all;
//             animation: qcodehover .3s linear;
//             animation-direction: alternate;
//             display: none;
//             z-index: 2;
//             img {
//                 width: 120px;
//                 height: 146px;
//             }
//         }
//         .go-iconqrcode {
//             @extend .icon;
//             margin-top: 6px;
//             height: 40px;
//             width: 40px;
//             background-position: 0 -40px;
//             z-index: 2;
//             cursor: pointer;
//             @media (max-width: $screen-md) {
//                 display: none;
//             }
//         }
//         &:hover {
//             .qrbody {
//                 display: block;
//             }
//         }
//     }
//     @media
//         right: 0;
//     }
// }

.no-show {
  display: none;
}

.show {
  display: block;
}

.cartoon-block {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  transition: all 1s;
  background-color: rgba($color: #050e20, $alpha: 1);
  overflow: hidden;
  .cartoon-body {
    position: absolute;
    top: 0;
    left: 50%;
    bottom: 0;
    right: 0;
    transform: translateX(-50%);
    z-index: 10;
    width: 100%;
    .tsa-logo {
      position: absolute;
      top: 12.88%;
      left: 0%;
      display: inline-block;
      width: 176px;
      height: 36px;
      background-image: url(../assets/tsa_logo.png);
      background-repeat: no-repeat;
      background-size: 176px 36px;
    }
    .tsa-title {
      position: absolute;
      left: 0%;
      top: 49%;
      display: inline-block;
      width: 350px;
      line-height: 1.8;
      color: #fff;
      font-size: 56px;
    }
    .tsa-content {
      position: absolute;
      right: 0%;
      top: 50%;
      display: inline-block;
      width: 50.5%;
      color: #fff;
      line-height: 1.5;
      font-size: 24px;
      p {
        margin-bottom: 24px;
        line-height: 1.5;
        letter-spacing: 2px;
      }
      .tsa-part {
        font-weight: lighter;
        display: inline-block;
        width: 89%;
      }
    }
    .cartoon-mblock {
      position: relative;
      height: 100%;
      margin: 0 100px;
    }
    .tsa-res {
      position: absolute;
      top: 0;
      right: 0;
      transform-origin: 100%;
      transform: rotate(90deg) translateY(29%) translateX(95%);
      display: inline-block;
      width: 350px;
      font-family: Roboto;
      font-weight: lighter;
      letter-spacing: 1px;
      font-size: 14px;
      color: rgba($color: #fff, $alpha: 0.6);
    }
    @keyframes tcodehover {
      0% {
        transform: scale(0) translate3d(-78%, 0%, 0);
      }
      100% {
        transform: scale(1) translate3d(-78%, 0%, 0);
      }
    }
    .tsa-qcode {
      @extend .qicon;
      position: absolute;
      top: 12.88%;
      right: 0%;
      height: 22px;
      width: 22px;
      background-position: -14px 0;
      cursor: pointer;
      .qrblock {
        position: relative;
        height: 22px;
        .qrbody {
          position: absolute;
          left: 0%;
          top: 0%;
          transform: scale(1) translate3d(-78%, 0%, 0);
          transform-origin: 20% 0%;
          animation: tcodehover 0.3s linear;
          display: none;
          img {
            width: 120px;
            height: 146px;
          }
        }
        &:hover {
          .qrbody {
            display: block;
          }
        }
      }
    }
    @keyframes jump {
      0% {
        bottom: 14.32%;
      }
      50% {
        bottom: 16%;
      }
      100% {
        bottom: 14.32%;
      }
    }
    .tsa-godown {
      @extend .qicon;
      position: absolute;
      bottom: 14.32%;
      left: 50%;
      height: 28px;
      width: 14px;
      transform: translateX(-50%);
      background-position: 0 0;
      animation: jump 3s ease both infinite;
    }
    .cartoon-top {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 12;
      background-color: rgba($color: #000, $alpha: 0.3);
    }
    @keyframes twinkling {
      0% {
        opacity: 1;
        transform: scale(1);
      }
      100% {
        opacity: 0.5;
        transform: scale(0.8);
      }
    }
    @keyframes appear {
      0% {
        opacity: 0.5;
        transform: scale(2);
      }
      50% {
        transform: scale(0.8);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    @keyframes waterdrop {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    @keyframes rotate {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(-360deg);
      }
    }
    .cartoon-bottom {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 50%;
      transform: translateX(-61.5%);
      z-index: 11;
      .cartoon-bottombody {
        position: relative;
        width: 100%;
        height: 100%;
        animation: rotate 600s linear infinite;
      }
      .svg-block {
        position: relative;
        width: 100%;
        height: 100%;
        svg {
          position: absolute;
        }
      }
      i {
        display: inline-block;
        border-radius: 100%;
        animation: twinkling 3s ease both infinite;
        animation-direction: alternate;
      }
      .i-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate3d(-50%, -50%, 0);
        .center-block {
          //animation: appear 1s ease-out;
          animation-fill-mode: forwards;
        }
        i {
          width: 30px;
          height: 30px;
          background-color: #00b2ff;
          animation-delay: 1.5s;
          box-shadow: 0px 0px 50px 0px #00b2ff;
        }
      }
      .bg-block-00,
      .bg-block-01,
      .bg-block-02,
      .bg-block-03,
      .bg-block-04,
      .bg-block-05 {
        display: inline-block;
        width: 2630px;
        height: 2630px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
        background-repeat: no-repeat;
        opacity: 0;
        animation: waterdrop 3s ease;
        animation-fill-mode: forwards;
        .bg-block {
          position: relative;
          width: 100%;
          height: 100%;
          opacity: 0;
          animation: waterdrop 0.5s ease;
          animation-fill-mode: forwards;
          animation-delay: 0.5s;
          i {
            position: absolute;
            border-radius: 100%;
            opacity: 0;
            animation: twinkling 3s ease infinite alternate-reverse;
            &:nth-of-type(1) {
              top: 42.2%;
              left: 48.1%;
              width: 30px;
              height: 30px;
              background-color: #e72d30; // animation-delay: random(6) + s;
            }
            &:nth-of-type(2) {
              top: 42.62%;
              left: 40.5%;
              width: 20px;
              height: 20px;
              background-color: #143166; // animation-delay: random(6) + s;
            }
            &:nth-of-type(3) {
              top: 40.6%;
              left: 56.78%;
              width: 20px;
              height: 20px;
              background-color: #00a74a; // animation-delay: random(6) + s;
            }
            &:nth-of-type(4) {
              top: 64.56%;
              left: 51.33%;
              width: 30px;
              height: 30px;
              background-color: #00a74a; // animation-delay: random(6) + s;
            }
            &:nth-of-type(5) {
              top: 41.74%;
              left: 32.12%;
              width: 30px;
              height: 30px;
              background-color: #fac323; // animation-delay: random(6) + s;
            }
            &:nth-of-type(6) {
              top: 47.52%;
              left: 64.71%;
              width: 24px;
              height: 24px;
              background-color: #fac323; // animation-delay: random(6) + s;
            }
            &:nth-of-type(7) {
              top: 31.74%;
              left: 56.768%;
              width: 30px;
              height: 30px;
              background-color: #143166; // animation-delay: random(6) + s;
            }
            &:nth-of-type(8) {
              top: 56.31%;
              left: 67.52%;
              width: 20px;
              height: 20px;
              background-color: #143166; // animation-delay: random(6) + s;
            }
            &:nth-of-type(9) {
              top: 58.63%;
              left: 28.59%;
              width: 20px;
              height: 20px;
              background-color: #e72d30; // animation-delay: random(6) + s;
            }
            &:nth-of-type(10) {
              top: 41.14%;
              left: 70.79%;
              width: 30px;
              height: 30px;
              background-color: #e72d30; // animation-delay: random(6) + s;
            }
            &:nth-of-type(11) {
              top: 70.22%;
              left: 33.04%;
              width: 30px;
              height: 30px;
              background-color: #00b2ff; // animation-delay: random(6) + s;
            }
            &:nth-of-type(12) {
              top: 67.98%;
              left: 63.65%;
              width: 20px;
              height: 20px;
              background-color: #e72d30; // animation-delay: random(6) + s;
            }
            &:nth-of-type(13) {
              top: 65%;
              left: 38%;
              width: 30px;
              height: 30px;
              background-color: #143166; // animation-delay: random(6) + s;
            }
            &:nth-of-type(14) {
              top: 33%;
              left: 29%;
              width: 20px;
              height: 20px;
              background-color: #00b2ff; // animation-delay: random(6) + s;
            }
            &:nth-of-type(15) {
              top: 58.8%;
              left: 42.5%;
              width: 20px;
              height: 20px;
              background-color: #fac323; // animation-delay: random(6) + s;
            }
            &:nth-of-type(16) {
              top: 29%;
              left: 66%;
              width: 20px;
              height: 20px;
              background-color: #fac323; // animation-delay: random(6) + s;
            }
            &:nth-of-type(17) {
              top: 70.87%;
              left: 22.58%;
              width: 20px;
              height: 20px;
              background-color: #00a74a; // animation-delay: random(6) + s;
            }
            &:nth-of-type(18) {
              top: 52.2%;
              left: 19.39%;
              width: 30px;
              height: 30px;
              background-color: #143166; // animation-delay: random(6) + s;
            }
            &:nth-of-type(19) {
              top: 23.04%;
              left: 16.69%;
              width: 30px;
              height: 30px;
              background-color: #00a74a; // animation-delay: random(6) + s;
            }
            &:nth-of-type(20) {
              top: 44.03%;
              left: 11.71%;
              width: 30px;
              height: 30px;
              background-color: #00b2ff; // animation-delay: random(6) + s;
            }
            &:nth-of-type(21) {
              top: 37%;
              left: 81%;
              width: 30px;
              height: 30px;
              background-color: #00b2ff; // animation-delay: random(6) + s;
            }
            &:nth-of-type(22) {
              top: 45%;
              left: 87%;
              width: 30px;
              height: 30px;
              background-color: #fac323; // animation-delay: random(6) + s;
            }
          }
        }
      }
      .bg-block-01 {
        //background-image: url('../assets/star1.png');
        animation-delay: 0s;
        .svg-block {
          svg {
            &:nth-of-type(1) {
              left: 46.1%;
              top: 46.1%;
            }
            &:nth-of-type(2) {
              left: 52.5%;
              top: 50%;
            }
            &:nth-of-type(3) {
              left: 42%;
              top: 38.5%;
            }
            &:nth-of-type(4) {
              left: 44.2%;
              top: 58.1%;
            }
            &:nth-of-type(5) {
              left: 34.6%;
              top: 35.2%;
            }
            &:nth-of-type(6) {
              left: 59.5%;
              top: 33.5%;
            }
            &:nth-of-type(7) {
              left: 27%;
              top: 30.2%;
            }
            &:nth-of-type(8) {
              left: 65.5%;
              top: 45%;
            }
            &:nth-of-type(9) {
              left: 36%;
              top: 31.5%;
            }
            &:nth-of-type(10) {
              left: 19.5%;
              top: 22.6%;
            }
            &:nth-of-type(11) {
              left: 76.5%;
              top: 50%;
            }
          }
        }
      }
      .bg-block-02 {
        //background-image: url('../assets/star2.png');
        animation-delay: 0.5s;
        .svg-block {
          svg {
            &:nth-of-type(1) {
              left: 51.4%;
              top: 46.5%;
            }
            &:nth-of-type(2) {
              left: 42.4%;
              top: 42.9%;
            }
            &:nth-of-type(3) {
              left: 58%;
              top: 41.9%;
            }
            &:nth-of-type(4) {
              left: 53.9%;
              top: 50.6%;
            }
            &:nth-of-type(5) {
              left: 40.4%;
              top: 59.6%;
            }
            &:nth-of-type(6) {
              left: 30.1%;
              top: 61.3%;
            }
            &:nth-of-type(7) {
              left: 20%;
              top: 55.5%;
            }
            &:nth-of-type(8) {
              left: 50%;
              top: 19.5%;
            }
            &:nth-of-type(9) {
              left: 42%;
              top: 16.4%;
            }
            &:nth-of-type(10) {
              left: 50%;
              top: 12%;
            }
            &:nth-of-type(11) {
              left: 12%;
              top: 50%;
            }
            &:nth-of-type(12) {
              left: 50%;
              top: 50%;
            }
            &:nth-of-type(13) {
              left: 84.8%;
              top: 15.2%;
            }
            &:nth-of-type(14) {
              left: 5%;
              top: 4.5%;
            }
            &:nth-of-type(15) {
              left: 33.5%;
              top: 31%;
            }
            &:nth-of-type(16) {
              left: 25.5%;
              top: 74.5%;
            }
            &:nth-of-type(17) {
              left: 8%;
              top: 27.5%;
            }
          }
        }
      }
      .bg-block-03 {
        //background-image: url('../assets/star3.png');
        animation-delay: 1s;
        .svg-block {
          svg {
            &:nth-of-type(1) {
              left: 46.3%;
              top: 51.2%;
            }
            &:nth-of-type(2) {
              left: 42.6%;
              top: 52.2%;
            }
            &:nth-of-type(3) {
              left: 49.8%;
              top: 42.2%;
            }
            &:nth-of-type(4) {
              left: 38.5%;
              top: 44.2%;
            }
            &:nth-of-type(5) {
              left: 37%;
              top: 58%;
            }
            &:nth-of-type(6) {
              left: 50.3%;
              top: 34.8%;
            }
            &:nth-of-type(7) {
              left: 30.8%;
              top: 44.2%;
            }
            &:nth-of-type(8) {
              left: 44%;
              top: 27.2%;
            }
            &:nth-of-type(9) {
              left: 23.2%;
              top: 36.2%;
            }
            &:nth-of-type(10) {
              left: 31.2%;
              top: 23.2%;
            }
            &:nth-of-type(11) {
              left: 56.5%;
              top: 57.2%;
            }
            &:nth-of-type(12) {
              left: 74.5%;
              top: 41.2%;
            }
            &:nth-of-type(13) {
              left: 15.5%;
              top: 20.5%;
            }
            &:nth-of-type(14) {
              left: 79.5%;
              top: 29.5%;
            }
            &:nth-of-type(15) {
              left: 13.5%;
              top: 13.5%;
            }
            &:nth-of-type(16) {
              left: 4%;
              top: 50%;
            }
          }
        }
      }
      .bg-block-04 {
        //background-image: url('../assets/star4.png');
        animation-delay: 2s;
      }
      .bg-block-05 {
        //background-image: url('../assets/star5.png');
        animation-delay: 2.5s;
      }
    }
    @media (max-width: $screen-xl) {
      .tsa-title {
        width: 400px;
        font-size: 36px;
      }
      .tsa-content {
        font-size: 16px;
      }
    }
    @media (max-width: $screen-md) {
      .cartoon-bottom {
        transform: scale(0.8) translateX(-61.5%) translateY(-25%);
      }
      .tsa-qcode,
      .tsa-res {
        display: none;
      }
      .tsa-godown {
        left: 25px;
        bottom: 48px;
      }
      .tsa-logo {
        top: 25px;
        left: 25px;
      }
      .tsa-title {
        font-size: 36px;
        width: 224px;
        line-height: 46px;
        top: 233px;
        left: 25px;
      }
      .tsa-content {
        font-size: 14px;
        width: 325px;
        top: 373px;
        left: 25px;
        p {
          margin-bottom: 10px;
          line-height: 24px;
        }
      }
      .cartoon-mblock {
        margin: 0;
      }
    }
  }
}

.no-cartoon {
  top: -100% !important;
  transition: top 1s;
}

.two-link {
  text-align: center;
  font-size: 16px;
  @media (max-width: $screen-md) {
    font-size: 14px;
  }
  .line {
    margin: 0 12px;
    color: rgba($color: #000000, $alpha: 0.1);
  }
  a {
    color: rgba($color: #0f1f3c, $alpha: 0.4);
    transition: all 0.8s;
    &:first-child {
      color: #00b2ff;
    }
    &:hover {
      color: #00b2ff;
    }
  }
  .sec-a {
    color: rgba($color: #0f1f3c, $alpha: 0.4);
    &:active,
    &:focus,
    &:visited {
      color: rgba($color: #0f1f3c, $alpha: 0.4);
    }
    &:hover {
      color: #00b2ff;
    }
  }
}

.tsastitle {
  margin: 40px 0 12px;
  font-size: 16px;
  line-height: 24px;
  color: #00a2ff;
  .hook {
    display: inline;
    padding-top: 150px;
  }
  @media (max-width: $screen-md) {
    margin: 20px 0 6px;
    font-size: 12px;
  }
}

img {
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.zip {
  height: 100%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 100%;
  }
}
