@use "../styles/default" as *;
.nav {
  margin: 0;
  display: flex;
  align-items: center;
  height: 100%;
  transition: all 0.8s;

  // @media (max-width: $screen-md) {
  //     padding: 0;
  //     display: flex;
  //     height: 62px;
  //     justify-content: space-around;
  //     border-top: 1px solid rgba($color: #fff, $alpha: 0.1);
  //     border-bottom: 1px solid rgba($color: #fff, $alpha: 0.1);
  // }
}

.no-border {
  border: 0;
}

.nav-a {
  position: relative;
  margin-left: 40px;
  color: $deep-bg-text-color;
  font-size: 16px;
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
  &::after {
    content: "";
    display: inline-block;
    position: absolute;
    bottom: -25px;
    left: 50%;
    height: 6px;
    width: 0;
    z-index: 10;
    border-radius: 3px;
    transform: translateX(-50%);
  }
  &:hover {
    color: #fff;
    font-weight: 600;
    font-family: PingFangSC-Medium;
  }

  @media (max-width: $screen-lg) {
    margin-left: 24px;
    font-size: 14px;
  }
  @media (max-width: $screen-md) {
    font-size: 14px;
    // margin-left: 0;
  }
}

.active {
  @extend .nav-a;
  color: #fff;
  font-weight: 600;
  &::after {
    width: 40px;
    background-color: #fff;
  }
}

.nav-black {
  transition: all 0.8s;
  .nav-a {
    color: $desc-text-color;
    &:hover {
      color: $primary;
    }
  }
  .active {
    color: $primary;
    &::after {
      background-color: $primary;
    }
  }
}
