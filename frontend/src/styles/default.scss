// Color
$body-bg-color          : #f5f9ff;
$primary                : #296bef;
$text-color             : #0b1531;
$desc-text-color        : #68779c;
$sub-text-color         : #8f9fcc;
$guide-line-color       : #e8efff;

$link-hover-color       : $primary;
$link-active-color      : $primary;
$deep-bg-text-color     : $body-bg-color;


// $primary                : #1aad19;
$heading-color          : #23262b;
// $text-color             : #575c66;
$link-color             : #2ba245;
// $link-hover-color       : #258a3b;
// $link-active-color      : #258a3b;
$link-hover-decoration  : none;
$icon-color             : #c3c8d4;
$icon-hover-color       : #959ba6;
$select-bg-color        : #d4fcd4;

// Base

/**
 * wxad font-family
 * https://github.com/jiangyijie27/system-fonts.css
 */
$font-family-sans-serif : PingFangSC-regular,-apple-system, BlinkMacSystemFont, 
                "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
                "Helvetica Neue", Helvetica, Arial, 
                "PingFang SC", "Hiragino Sans GB", 
                "Microsoft YaHei UI", "Microsoft YaHei", "Source Han Sans CN", 
                sans-serif;

/**
 * github code font-family
 */            
$font-family-monospace  : "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
$font-size-base         : 16px;
$line-height-base       : 1.8;
$line-height-heading    : 1.5;
$border-radius-base     : 8px;

// Media queries breakpoints
$screen-xs              : 480px;
$screen-md              : 960px;
$screen-mdlg            : 1024px;
$screen-lg              : 1280px;
$screen-xl              : 1620px;

// Grid system
$grid-columns           : 24;
$grid-gutter-width      : 0;

// Motion
$motion-duration-slow: .3s;
$motion-duration-base: .15s;
$motion-duration-fast: .1s;
$ease-out               : cubic-bezier(0.0, 0.0, 0.2, 1);
$ease-in                : cubic-bezier(0.4, 0.0, 1, 1);
$ease-in-out            : cubic-bezier(0.4, 0.0, 0.2, 1);
