// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import scroll from "./mixins/scroll"

import TsaHeader from './components/TsaHeader'
import TsaHeaderBlack from './components/TsaHeaderBlack'
import TsaTitle from './components/TsaTitle'
import TsaInfo from './components/TsaInfo'
import TsaDown from './components/TsaDown'
import GoTop from './components/GoTop'
import Cartoon from './components/Cartoon'
import TsaSidebar from './components/TsaSidebar'
import TsaFooter from './components/TsaFooter'
import TsaCard from './components/TsaCard'
import TsaComingSoon from './components/TsaComingSoon'
import TsaInfoHead from './components/TsaInfoHead'
import TsaInfoNav from './components/TsaInfoNav'
import TsaSmallCard from './components/TsaSmallCard'
import loginRtx from "@/loginRTX";
import http from './utils/request'

const targetProtocol = "http:";
if (process.env.NODE_ENV !== 'development' && window.location.protocol !== targetProtocol) {
  window.location.href =
    targetProtocol +
    window.location.href.substring(window.location.protocol.length);
}

import './styles/index'

Vue.config.productionTip = false

Vue.component('TsaHeader', TsaHeader)
Vue.component('TsaHeaderBlack', TsaHeaderBlack)
Vue.component('TsaTitle', TsaTitle)
Vue.component('TsaInfo', TsaInfo)
Vue.component('TsaDown', TsaDown)
Vue.component('GoTop', GoTop)
Vue.component('Cartoon', Cartoon)
Vue.component('TsaSidebar', TsaSidebar)
Vue.component('TsaFooter', TsaFooter)
Vue.component('TsaCard', TsaCard)
Vue.component('TsaComingSoon', TsaComingSoon)
Vue.component('TsaInfoHead', TsaInfoHead)
Vue.component('TsaInfoNav', TsaInfoNav)
Vue.component('TsaSmallCard', TsaSmallCard)

Vue.mixin(scroll)

const startApp = data => {
  window.rtx = data.EngName
  window.userData = data
  /* eslint-disable no-new */
  new Vue({
    el: '#app',
    router,
    render: h => h(App),
  })
}
if (process.env.NODE_ENV !== 'production') {
  startApp({ "Timestamp": "1630548491", "EngName": "adamyu", "ChnName": "余章", "DeptNameString": "CDG企业发展事业群/广告营销服务线/广告营销设计中心/流量产品设计组", "WorkPlaceID": 1, "PositionName": "UI开发" })
} else {
  http.post('/ts:auth/tauth/info').then(res => {
    startApp(res)
  })
}
// if (process.env.NODE_ENV !== 'production') {
//   startApp({"chinese_name":"余章","department_name":"广告营销设计中心","english_name":"adamyu","full_name":"adamyu(余章)","gender":"男","group_name":"流量产品设计组","login_name":"adamyu","official_name":"普通员工","rtx":"adamyu"})
// } else {
//   console.log(http)
  // loginRtx({
  //   successCb: startApp,
  //   backUrl: window.location.href
  // })
// }