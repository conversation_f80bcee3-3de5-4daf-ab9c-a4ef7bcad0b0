<script>
export default {
  name: "TsaInfo",

  props: ["info"],
  mounted() {
    console.log(this.info);
  },
};
</script>

<template>
  <div>
    <div v-for="(item, index) in info" :key="index">
      <div
        :id="item.id"
        :class="item.noboard ? 'info-borderblock' : 'info-block'"
      >
        <div class="word-block">
          <p class="title">{{ item.title }}</p>
          <p
            class="info-content"
            v-for="(item, key) in item.content"
            :key="key"
          >
            {{ item }}
          </p>
          <p
            class="info-pcontent"
            v-for="(item, key) in item.pcontent"
            :key="key"
          >
            {{ item }}
          </p>
        </div>
        <div class="pic-block">
          <img
            v-for="(imgItem, key) in item.img"
            :key="key"
            :src="imgItem"
            class="img"
            :class="{ specImg: item.isSpecImg }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";
.info-block,
.info-borderblock {
  padding: 0;
  .word-block,
  .pic-block {
    width: 100%;
  }
  .pic-block {
    display: flex;
    justify-content: space-between;
    align-items: start;
    flex-wrap: wrap;
    margin-top: 30px;
    .img {
      max-width: 340px;
      // height: 173px;
      margin-bottom: 40px;
    }
    .specImg {
      max-width: 100%;
    }
  }
  .title {
    position: relative;
    margin-bottom: 16px;
    font-family: PingFangSC-Medium;
    font-size: 24px;
    color: $text-color;
    letter-spacing: 1px;
    line-height: 1.5;
  }
  .info-content {
    margin-bottom: 12px;
    line-height: 1.8;
    font-size: 16px;
    color: $text-color;
    letter-spacing: 0.5px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .info-pcontent {
    line-height: 1.8;
    font-size: 16px;
    color: $text-color;
    letter-spacing: 1px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  @media (max-width: $screen-lg) {
    // padding: 0 0 32px;
    flex-wrap: wrap;
    .word-block,
    .pic-block {
      padding: 0;
      width: 100%;
      .img {
        max-width: 308px;
        margin-bottom: 32px;
      }
    }
    .word-block {
      margin-bottom: 20px;
    }
    .title {
      font-size: 18px;
      letter-spacing: 0.8px;
      line-height: 1.5;
    }
    .info-content {
      font-size: 14px;
      margin-bottom: 8px;
    }
    .info-pcontent {
      font-size: 14px;
    }
  }
}

.info-borderblock {
  @extend .info-block;
  padding: 0 0 72px;
  margin-bottom: 72px;
  border-bottom: 1px solid #e1e4e6;
  @media (max-width: $screen-lg) {
    padding: 0 0 40px;
    margin-bottom: 40px;
  }
}
</style>
