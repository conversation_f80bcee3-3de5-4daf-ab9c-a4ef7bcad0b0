
<template>
<div>
  <BoxHead>
    <div slot='title'>邮件签名范例</div>
  </BoxHead>

  <BoxContent verticalCenter='true'>
    <img style2='margin-top: 0px;' class="demo-img" :src="avatarData" alt="">
  </BoxContent>

</div>
</template>

<script>
import BoxHead from '../components/BoxHead.vue'
import BoxContent from '../components/BoxContent.vue'
import avatarDemo from '@/assets/img/demo.png'

export default {
  components: {
    BoxHead,
    BoxContent
  },
  props: ['team'],
  data() {
    return {
      avatarData: avatarDemo
    }
  }
}
</script>
<style lang="scss" scoped>
.demo-img {
  position: relative;
  top: -20px;
  width: 556px;
}
</style>