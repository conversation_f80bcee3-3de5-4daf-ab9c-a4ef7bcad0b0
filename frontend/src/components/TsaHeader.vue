<script>
import Header<PERSON>ink from "./HeaderLink";

export default {
  name: "T<PERSON><PERSON>ead<PERSON>",
  components: { HeaderLink },
  props: ["title", "index", "blueBlock"],
  data() {
    return {
      showShadow: true,
    };
  },
  methods: {
    goCartoon() {
      this.$router.push("/");
    },
  },
};
</script>

<template>
  <section
    ref="headerTop"
    class="header"
    :class="{ showShadow: showShadow }"
    style="top: 0"
  >
    <header class="header__head">
      <div class="header__head-top">
        <a href="javascript:;" v-on:click="goCartoon">
          <div class="logo-block">
            <i class="logo"></i>
          </div>
        </a>
        <div>
          <HeaderLink :index="index"></HeaderLink>
        </div>
      </div>
      <!--      <div class="blue-block" v-if="blueBlock"></div>-->
    </header>
  </section>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-width: 1024px;
  // transform: translateX(-50%);
  background: $primary;
  z-index: 2;
  &__head {
    margin: 0 auto;
    max-width: 1200px;

    @media (max-width: $screen-lg) {
      max-width: 984px;
    }

    &-top {
      height: 72px;
      display: flex;
      justify-content: space-between;
    }

    .blue-block {
      position: relative;
      height: 8px;

      &:after {
        content: "";
        display: inline-block;
        position: absolute;
        top: 0;
        left: 0;
        background-color: #0099ff;
        width: 100%;
        height: 8px;
        z-index: 1;
      }

      // @media (max-width: $screen-md) {
      //   margin: 0 12px;
      // }
    }

    .logo-block {
      color: #fff;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .logo {
      display: inline-block;
      width: 282px;
      height: 28px;
      background-image: url(../assets/tsa_logo_white_new.png);
      background-repeat: no-repeat;
      background-size: 282px 28px;

      @media (max-width: $screen-lg) {
        width: 220px;
        height: 22px;
        background-size: 220px 22px;
      }
    }

    .logo-title {
      display: inline-block;
      margin: 0 0 0 16px;
      padding: 0 0 0 16px;
      height: 20px;
      line-height: 20px;
      color: #fff;
      border-left: 1px solid #fff;
      font-size: 20px;
    }
  }
}
.showShadow {
  box-shadow: 0 0 20px 5px $primary;
}
</style>
