<script>
export default {
  name: "HeaderLink",

  props: ["index", "isBlack"],
  data() {
    return {
      isMoible: false,
      links: [
        {
          name: "形象规范",
          link: "/Brand",
        },

        {
          name: "公关话术",
          link: "/PublicRelations",
        },

        {
          name: "资源下载",
          link: "/Download",
        },
        {
          name: "工具箱",
          link: "/Toolbox",
        },
      ],
    };
  },
  methods: {
    topBorder() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      let headerHeight = 63;

      if (scrollTop < headerHeight) {
        this.isMoible = false;
      } else {
        this.isMoible = true;
      }
    },
    toRouter(route) {
      document.documentElement.scrollTop = 0;
      this.$router.push(route);
    },
  },
  mounted() {
    let m_width = document.body.clientWidth;

    if (m_width <= 960) {
      window.addEventListener("scroll", this.topBorder);
    }
  },
};
</script>

<template>
  <ol class="nav" :class="{ 'no-border': isMoible, 'nav-black': isBlack }">
    <li v-for="(item, activeindex) in links" :key="activeindex">
      <!--			<router-link :class="activeindex == index ? 'active' : 'nav-a'" :to="item.link">{{item.name}}</router-link>-->
      <span
        :class="activeindex == index ? 'active' : 'nav-a'"
        class="header_item"
        @click="toRouter(item.link)"
        >{{ item.name }}</span
      >
    </li>
  </ol>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@import "../styles/headerlink";
.header_item {
  cursor: pointer;
}
</style>
