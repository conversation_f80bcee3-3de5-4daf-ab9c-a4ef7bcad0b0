<script>
export default {
  name: "TsaInfoNav",
  props: ["titleLeft", "linkLeft", "titleRight", "linkRight"],
};
</script>

<template>
  <div class="info-nav">
    <router-link class="nav nav-left" :to="linkLeft">
      <div class="nav-icon" alt=""></div>
      <div class="nav-title">{{ titleLeft }}</div>
    </router-link>
    <router-link class="nav nav-right" :to="linkRight">
      <div class="nav-title">{{ titleRight }}</div>
      <div class="nav-icon" alt=""></div>
    </router-link>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style scoped lang="scss">
@use "../styles/default" as *;
.info-nav {
  border-top: 2px solid $guide-line-color;
  padding: 21.5px 0;
  width: 100%;
  .nav {
    font-size: 16px;
    color: $sub-text-color;
    display: flex;
    align-items: center;
    transition: all 0.3s;
    cursor: pointer;
    @media screen and (max-width: $screen-lg) {
      font-size: 14px;
    }
    .nav-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      background-position: center;
      @media screen and (max-width: $screen-lg) {
        width: 14px;
        height: 14px;
      }
    }
    &:hover {
      color: $primary;
      font-family: PingFangSC-Medium;
    }
  }
  .nav-left {
    justify-content: flex-start;
    float: left;
    .nav-icon {
      margin-right: 10px;
      background-image: url("../assert/ICON/Icon_arrow_left.svg");
    }
    &:hover {
      .nav-icon {
        background-image: url("../assert/ICON/Icon_arrow_left_hover.svg");
      }
    }
  }
  .nav-right {
    justify-content: flex-end;
    float: right;
    .nav-icon {
      margin-left: 10px;
      background-image: url("../assert/ICON/Icon_arrow.svg");
    }
    &:hover {
      .nav-icon {
        background-image: url("../assert/ICON/Icon_arrow_hover.svg");
      }
    }
  }
}
</style>
