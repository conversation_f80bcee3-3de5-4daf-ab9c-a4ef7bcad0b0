<script>
import Head<PERSON><PERSON>ink from "./HeaderLink";

export default {
  name: "TsaHeaderBlack",
  components: { HeaderLink },
  props: ["title", "index", "blueBlock"],

  methods: {
    moibleTop() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      let headerHeight = 63;

      // if (scrollTop < headerHeight) {
      //   this.$nextTick(() => { this.$refs.headerTop.style.top = '-' + scrollTop + 'px' })
      // } else {
      //   this.$nextTick(() => { this.$refs.headerTop.style.top = '-' + headerHeight + 'px' })
      // }
    },

    goCartoon() {
      window.location.reload();
      window.location.href = "/";
    },
  },

  mounted() {
    let m_width = document.body.clientWidth;

    if (m_width <= 960) {
      window.addEventListener("scroll", this.moibleTop);
    }

    // console.log(this.blueBlock);
  },
  beforeD<PERSON>roy() {
    window.removeEventListener("scroll", this.moibleTop);
  },
};
</script>

<template>
  <section ref="headerTop" class="header" style="top: 0">
    <header class="header__head">
      <div class="header__head-top">
        <a href="javascript:;" v-on:click="goCartoon">
          <div class="logo-block">
            <i class="logo"></i>
          </div>
        </a>
        <div>
          <HeaderLink :index="index" isBlack="true"></HeaderLink>
        </div>
      </div>
      <!--      <div class="blue-block" v-if="blueBlock"></div>-->
    </header>
  </section>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-width: 1024px;
  background: #fff;
  z-index: 2;
  box-shadow: rgba(44, 114, 255, 0.05) 0px 4px 20px;
  transition: top 0.4s cubic-bezier(0.19, 1, 0.22, 1) 0s;
  border-bottom: 1px solid rgba(44, 114, 255, 0.1);

  &__head {
    margin: 0 auto;
    max-width: 1200px;

    @media (max-width: $screen-lg) {
      max-width: 984px;
    }

    &-top {
      height: 72px;
      display: flex;
      justify-content: space-between;
    }

    .blue-block {
      position: relative;
      height: 8px;
      &:after {
        content: "";
        display: inline-block;
        position: absolute;
        top: 0;
        left: 0;
        background-color: $primary;
        width: 100%;
        height: 8px;
        z-index: 1;
      }

      @media (max-width: $screen-md) {
        margin: 0 12px;
      }
    }

    .logo-block {
      color: $desc-text-color;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .logo {
      display: inline-block;
      width: 280px;
      height: 28px;
      background-image: url(../assets/tsa_logo_black_new.png);
      background-repeat: no-repeat;
      background-size: 280px 28px;

      @media (max-width: $screen-lg) {
        width: 220px;
        height: 22px;
        background-size: 220px 22px;
      }
    }

    .logo-title {
      display: inline-block;
      margin: 0 0 0 16px;
      padding: 0 0 0 16px;
      height: 20px;
      line-height: 20px;
      color: #fff;
      border-left: 1px solid #fff;
      font-size: 20px;
    }

    // @media (max-width: $screen-md) {

    //   &-top {
    //     height: auto;
    //     display: block;
    //   }

    //   .logo-block {
    //     height: 62px;
    //     justify-content: center;

    //     .logo {
    //       width: 112px;
    //       height: 23px;
    //       background-size: 112px 23px;
    //     }

    //     .logo-title {
    //       margin: 0 0 0 8px;
    //       padding: 0 0 0 8px;
    //       height: 14px;
    //       line-height: 14px;
    //       font-size: 14px;
    //     }
    //   }
    // }
  }
}
</style>
