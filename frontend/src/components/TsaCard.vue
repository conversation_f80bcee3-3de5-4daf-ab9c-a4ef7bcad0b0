<script>
export default {
  name: "Tsa<PERSON><PERSON>",
  props: ["pic", "options"],
};
</script>

<template>
  <div class="card">
    <div class="card-pic">
      <img :src="pic" alt="" />
    </div>
    <div class="card-options">
      <div class="title">{{ options.title }}</div>
      <div
        class="content"
        v-for="(item, index) in options.content"
        :key="index"
      >
        {{ item }}
      </div>
      <div
        :class="{
          button: !options.button.disabled,
          'button-disabled': options.button.disabled,
        }"
      >
        <a
          class="button-a"
          :href="options.button.link"
          target="_blank"
          v-if="!options.button.disabled"
        >
          {{ options.button.value }}
        </a>
        <span v-else>{{ options.button.value }}</span>
      </div>
    </div>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;

.card {
  width: 100%;
  padding: 56px 108px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  @media screen and (max-width: $screen-lg) {
    padding: 56px 60px;
  }
  .card-pic {
    width: 416px;
    @media screen and (max-width: $screen-lg) {
      width: 339px;
      font-size: 14px;
    }
    img {
      width: 100%;
    }
  }
}
.card-options {
  max-width: 460px;
  .title {
    font-size: 36px;
    font-family: PingFangSC-Medium;
    margin-bottom: 54px;
    position: relative;
    @media screen and (max-width: $screen-lg) {
      font-size: 32px;
    }
    &::after {
      content: "";
      display: inline-block;
      position: absolute;
      bottom: -30px;
      left: 0;
      background-color: #ffa601;
      height: 6px;
      width: 40px;
      border-radius: 3px;
    }
  }
  .content {
    font-size: 16px;
    line-height: 28px;
    letter-spacing: 0.5px;
    color: $desc-text-color;
    @media screen and (max-width: $screen-lg) {
      font-size: 14px;
    }
  }
  // .button {
  //   width: 144px;
  //   height: 48px;
  //   margin-top: 32px;
  //   border-radius: 24px;
  //   background-color: $primary;
  //   .button-a {
  //     width: 100%;
  //     height: 100%;
  //     font-size: 16px;
  //     color: #fff;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //   }
  // }
  .button-disabled {
    margin-top: 32px;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    box-sizing: border-box;
    border-radius: 29px;
    background-color: #a3adcd;
    cursor: not-allowed;
    color: #fff;
    font-size: 16px;
    padding: 0 32px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    text-decoration: none;
    outline: 0;
    .button-a {
      cursor: not-allowed;
      width: 100%;
      height: 100%;
      font-size: 16px;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      @media screen and (max-width: $screen-lg) {
        font-size: 14px;
      }
    }
  }
  .button {
    margin-top: 32px;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    box-sizing: border-box;
    border-radius: 29px;
    background-color: #296bef;
    box-shadow: 0 1px 0 rgba(44, 114, 255, 0.08),
      0 6px 12px rgba(44, 114, 255, 0.18);
    cursor: pointer;
    color: #fff;
    font-size: 16px;
    padding: 0 32px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    text-decoration: none;
    outline: 0;
    transition: all 0.4s ease;
    &:hover {
      color: #fff;
      background-color: #165be0;
      box-shadow: none;
    }
    .button-a {
      width: 100%;
      height: 100%;
      font-size: 16px;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      @media screen and (max-width: $screen-lg) {
        font-size: 14px;
      }
    }
  }
}
</style>
