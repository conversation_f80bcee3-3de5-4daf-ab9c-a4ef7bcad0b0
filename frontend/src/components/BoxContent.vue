<style scoped>
.root {
  min-height: 500px;
}
</style>

<template>
<div class='root' :style='{height:height}'>
  <template v-if='verticalCenter'>
    <VerticalCenter style='min-height:inherit;'><slot></slot></VerticalCenter>
  </template>
  <template v-else><slot></slot></template>
</div>
</template>

<script>
import VerticalCenter from './VerticalCenter.vue'
export default {
  components: {VerticalCenter},
  props: ['verticalCenter', 'height']
}
</script>
