<style scoped>
.fullwidth {
  width: 1000px;
  margin: 0 auto;
}

.clearfix {
  zoom: 1;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
.clearfix:before {
  content: "";
  display: block;
}

.pull-left {
  float: left;
}
.pull-right {
  float: right;
}

.divide {
  height: 0;
  width: 100%;
  border-bottom: 1px #eef0f4 solid;
}
a {
  color: #459ae9;
  text-decoration: none;
}
header {
  height: 400px;
  background: #0a1533;
  overflow: auto;
}
.logo {
  font-size: 24px;
  color: #ffffff;
  letter-spacing: 1px;
  margin-top: 80px;
  display: inline-block;
}
.logo:hover {
  text-decoration: none;
}
.line {
  width: 35px;
  height: 1px;
  background: #d4dade;
  margin: 16px 0;
}
.meta {
  font-size: 14px;
  color: #d4dade;
  letter-spacing: 1px;
}
.desc {
  font-size: 14px;
  margin-top: 76px;
  color: #949494;
  letter-spacing: 0;
  line-height: 24px;
}

.router-view-content {
  position: relative;
  margin-top: -152px;
  margin-bottom: 40px;
  background-color: #fff;
  box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 24px 48px 0;
}
footer {
  margin-bottom: 20px;
  text-align: center;
}
svg {
  vertical-align: middle;
  margin-top: -2px;
}
.tad {
  font-size: 14px;
  color: #949494;
  margin-left: 17px;
}
.contact {
  font-size: 14px;
  color: #949494;
  letter-spacing: 0;
}
</style>

<template>
  <div>
    <header>
      <div style="position: relative" class="fullwidth">
        <div class="pull-left">
          <router-link to="/" class="logo" exact>
            AMS邮件签名生成工具</router-link
          >
          <div class="line"></div>
          <p class="meta">
            邮件签名生成工具，为团队成员提供更加专业化、品牌化的对外展示形象。
          </p>
        </div>
      </div>
    </header>
    <div class="fullwidth">
      <keep-alive>
        <router-view class="router-view-content" :team="team"></router-view>
      </keep-alive>
    </div>

    <footer>
      <div class="fullwidth" style="padding-bottom: 96px">
        <div class="pull-left">
          <!-- <svg width="76" height="16" viewBox="0 0 76 16" xmlns="http://www.w3.org/2000/svg"><title>Combined Shape</title><path d="M41.556 11.977l-4.89-7.954-7.333 11.93h9.334-5.334l2.364-3.976h5.859zM39.066.047l9.6 15.906h-24l9.6-15.906h4.8zm-29.054 0H0v3.976h10.012V16h3.976V4.023h9.345V.047h-9.345V0h-3.976v.047zm42.655 0h15.385C72.442.047 76 3.617 76 8c0 4.393-3.57 7.953-7.948 7.953H52.667V.047zm15.617 3.622h-11.24v8h11.24a4.003 4.003 0 0 0 4.005-4c0-2.205-1.793-4-4.005-4z" fill="#000" fill-rule="evenodd" opacity=".3"/></svg> -->
          <svg
            style="fill: #a7a8ab"
            width="76"
            height="16"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 390 80.2"
          >
            <path
              class="st0"
              d="M390 40.9C390.5 18.4 371.6 0 349.2 0H275.1c-2.8 0-5.1 2.3-5.1 5.1V75c0 2.8 2.3 5.1 5.1 5.1H350c21.8 0 39.6-17.5 40-39.2zm-20-.1c-.4 10.9-9.6 19.3-20.5 19.3h-52.4c-2.4 0-4.6-1.7-4.9-4.2V25c0-2.8 2.2-5 5-5H350c11.3 0 20.4 9.4 20 20.8zM114.7 0H5.1C2.3 0 0 2.3 0 5.1V15c0 2.8 2.2 5 5 5h38.8c2.8 0 5 2.3 5 5v50c0 2.8 2.2 5 5 5H66c2.8 0 5-2.3 5-5V25c0-2.8 2.2-5 5-5h39c2.8 0 5-2.3 5-5V5.3c0-2.9-2.4-5.3-5.3-5.3zM244 72.1l-.6-.9s-3-4.3-3.7-5.7L200.3 2.4C199.4.9 197.7 0 196 0h-16.7c-1.5 0-2.8.7-3.8 1.7-.1.1-.2.2-.2.3-.1.1-.1.2-.2.3l-.1.1-43.3 69.4c-2.3 3.6.3 8.4 4.6 8.4H151l32.7-52.4c.9-1.2 2.3-2 4-2 1.7 0 3.2.9 4.1 2.2l15.4 24.6c.4.7.7 1.6.7 2.5 0 2.8-2.2 5-5 5h-19.6l-12.2 20H240c2.8 0 5-2.2 5-5 0-1.1-.4-2.2-1-3z"
            />
          </svg>
          <span class="tad">广告营销设计中心制作</span>
        </div>
        <div class="pull-right">
          <span class="contact">有问题请联系 Biubiucchen</span>
          <DivideLine color="#ccc" />
          <a :href="faqLink" target="_blank" class="special-link add pull-right"
            >使用说明</a
          >
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import DivideLine from "@/components/DivideLine.vue";
// import {fetchPosts} from '../api'
import pkg from "../../package.json";
export default {
  components: { DivideLine },
  data() {
    const team = "spa";
    let meta;
    return {
      team,
      faqLink: "#/MailAbout",
      meta,
      version: pkg.version,
      posts: null,
      postsOrder: 0,
    };
  },
  watch: {},
  methods: {},
  created() {},
};
</script>
