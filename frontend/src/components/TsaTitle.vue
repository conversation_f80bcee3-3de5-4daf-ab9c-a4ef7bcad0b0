<script>
export default {
  name: "TsaTitle",

  props: ["title", "content", "pic"],
};
</script>

<template>
  <div class="banner-container" ref="titleBlock" id="title">
    <div class="banner">
      <div class="title-block">
        <p class="title">{{ title }}</p>
        <div
          class="title-content"
          v-for="(item, index) in content"
          :key="index"
        >
          {{ item }}
        </div>
      </div>
      <div class="banner-pic-block">
        <img :src="pic" alt="" />
      </div>
    </div>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;

@keyframes BannerOpacity {
  0% {
    opacity: 0.5;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

@keyframes BannerTitleOpacity {
  0% {
    opacity: 0.5;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

.banner-container {
  background-color: $primary;
  padding-top: 72px;
  color: #fff;
  height: 452px;
  .banner {
    max-width: 1200px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: $screen-lg) {
      max-width: 984px;
    }
  }

  @media (max-width: $screen-lg) {
    height: 380px;
  }
}

.title-block {
  flex: 1;
  min-height: 135px;
  // animation: BannerTitleOpacity .5s ease forwards;

  .title {
    font-size: 48px;
    color: #fff;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .title-content {
    display: inline-block;
    width: 450px;
    line-height: 30px;
    font-size: 16px;
    color: #c4d4f5;
    font-weight: normal;
    letter-spacing: 1px;
  }
}
.banner-pic-block {
  width: 500px;
  animation: BannerOpacity 0.5s ease forwards;

  img {
    width: 100%;
  }
}
@media (max-width: $screen-lg) {
  .title-block {
    .title {
      font-size: 40px;
    }
    .title-content {
      font-size: 14px;
    }
  }
  .banner-pic-block {
    width: 420px;
  }
}
</style>
