<script>
let timer = null;

export default {
  name: "GoTop",

  props: ["index"],
  data() {
    return {
      isTop: true,
    };
  },
  methods: {
    needScroll() {
      window.onscroll = function () {
        if (!this.isTop) {
          clearInterval(timer);
        }
        this.isTop = false;
      };
    },

    goTop() {
      timer = setInterval(function () {
        let osTop =
          document.documentElement.scrollTop || document.body.scrollTop;
        let ispeed = Math.floor(-osTop / 5);
        document.documentElement.scrollTop = document.body.scrollTop =
          osTop + ispeed;
        this.isTop = true;
        if (osTop === 0) {
          clearInterval(timer);
        }
      }, 60);
    },
  },
  mounted() {
    this.needScroll();
  },
};
</script>

<template>
  <!-- 回到顶部板块 -->
  <div @click="goTop" class="go-top">
    <div class="go-topblock">
      <img src="../assert/ICON/backtop.svg" alt="" />
    </div>
    <div class="hover-tip">
      <div class="tip-block">返回顶部</div>
    </div>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";
// 底部板块
.go-top {
  position: fixed;
  right: 24px;
  bottom: 24px;
  transition: all 0.3s ease;
  .go-topblock {
    width: 64px;
    height: 64px;
    background-color: #fff;
    border-radius: 100%;
    border: 2px solid rgba($color: $primary, $alpha: 0.1);
    box-shadow: 0 4px 20px rgba($color: #0074ff, $alpha: 0.05);
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 18.4px;
      height: 29px;
    }
  }
  .hover-tip {
    display: none;
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateX(-143%) translateY(-50%);
    .tip-block {
      position: relative;
      background-color: rgb(11, 21, 49);
      font-size: 14px;
      white-space: nowrap;
      color: rgb(225, 229, 235);
      padding: 12px 16px;
      border-radius: 8px;
      &::after {
        content: "";
        position: absolute;
        right: 0px;
        top: 50%;
        transform: translate3d(45%, -50%, 0px) rotate(226deg);
        height: 0px;
        border-style: solid;
        border-width: 5px;
        border-radius: 3px;
        border-color: transparent transparent rgb(11, 21, 49) rgb(11, 21, 49);
      }
    }
  }
  &:hover {
    cursor: pointer;
    .go-topblock {
      border: 2px solid $primary;
    }
    .hover-tip {
      display: block;
    }
  }
}

@media (max-width: $screen-mdlg) {
  .go-top {
    display: none;
  }
}
</style>
