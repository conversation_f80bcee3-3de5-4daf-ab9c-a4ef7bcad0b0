<script>
export default {
  name: "TsaComingSoon",
};
</script>

<template>
  <div class="coming-soon">
    <div class="title">COMING SOON</div>
    <div class="content">内容正在筹备中，敬请期待。</div>
    <div class="pic">
      <img src="../assert/Coming/<EMAIL>" alt="" />
    </div>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
.coming-soon {
  margin: 0 auto;
  padding: 56px 0 70px 0;
  .title {
    text-align: center;
    font-size: 36px;
    font-family: PingFangSC-Medium;
    position: relative;
    margin-bottom: 40px;
    &::after {
      content: "";
      display: inline-block;
      position: absolute;
      bottom: -22px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #ffa601;
      height: 6px;
      width: 40px;
      border-radius: 3px;
    }
  }
  .content {
    text-align: center;
    font-size: 16px;
    line-height: 28px;
    letter-spacing: 0.8;
    color: $desc-text-color;
    margin-bottom: 24px;
    @media screen and (max-width: $screen-lg) {
      font-size: 14px;
    }
  }
  .pic {
    width: 412px;
    margin: 0 auto;
    img {
      width: 100%;
    }
  }
}
</style>
