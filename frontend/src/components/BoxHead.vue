<style scoped>
.root {
  padding-bottom: 24px;
  border-bottom: 1px solid #E1E7EB;
  overflow: hidden;
  line-height: 18px;
}
.title {
  font-size: 18px;
  color: #000000;
  letter-spacing: 0;
}
.close {
  color: #8C9195;
}
.close:hover {
  color: #000000;
}
.pull-left {
  float: left;
}
.pull-right {
  float: right;
}
</style>

<template>
<div class='root'>
  <div class='title pull-left'>
    <slot name="title">内容被分发且重新组合</slot>
  </div>
  <div class='pull-right'>
    <slot name="right">
      <router-link to="/Mail" class='close pull-right' exact>
        <svg style='vertical-align: middle; margin-top: -2px;' width="19" height="19" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 19 19"><path d="M9.5 8L1.8.3C1.4-.1.7-.1.3.3c-.4.4-.4 1.1 0 1.5L8 9.5.3 17.2c-.4.4-.4 1.1 0 1.5.4.4 1.1.4 1.5 0L9.5 11l7.7 7.7c.4.4 1.1.4 1.5 0 .4-.4.4-1.1 0-1.5L11 9.5l7.7-7.7c.4-.4.4-1.1 0-1.5-.4-.4-1.1-.4-1.5 0L9.5 8z" fill="#0a1533"/></svg>
        <!-- <svg id="图层_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 390 80.2"><style>.st0{fill:#a7a8ab}</style><path class="st0" d="M390 40.9C390.5 18.4 371.6 0 349.2 0H275.1c-2.8 0-5.1 2.3-5.1 5.1V75c0 2.8 2.3 5.1 5.1 5.1H350c21.8 0 39.6-17.5 40-39.2zm-20-.1c-.4 10.9-9.6 19.3-20.5 19.3h-52.4c-2.4 0-4.6-1.7-4.9-4.2V25c0-2.8 2.2-5 5-5H350c11.3 0 20.4 9.4 20 20.8zM114.7 0H5.1C2.3 0 0 2.3 0 5.1V15c0 2.8 2.2 5 5 5h38.8c2.8 0 5 2.3 5 5v50c0 2.8 2.2 5 5 5H66c2.8 0 5-2.3 5-5V25c0-2.8 2.2-5 5-5h39c2.8 0 5-2.3 5-5V5.3c0-2.9-2.4-5.3-5.3-5.3zM244 72.1l-.6-.9s-3-4.3-3.7-5.7L200.3 2.4C199.4.9 197.7 0 196 0h-16.7c-1.5 0-2.8.7-3.8 1.7-.1.1-.2.2-.2.3-.1.1-.1.2-.2.3l-.1.1-43.3 69.4c-2.3 3.6.3 8.4 4.6 8.4H151l32.7-52.4c.9-1.2 2.3-2 4-2 1.7 0 3.2.9 4.1 2.2l15.4 24.6c.4.7.7 1.6.7 2.5 0 2.8-2.2 5-5 5h-19.6l-12.2 20H240c2.8 0 5-2.2 5-5 0-1.1-.4-2.2-1-3z"/></svg> -->
      </router-link>
    </slot>
  </div>
</div>
</template>
