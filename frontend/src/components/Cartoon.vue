<script>
    let timer = null

    export default {
        name: "Cartoon",

        props: [
            'index'
        ],
        methods: {
 
        },
    };
</script>

<template>
<transition name="cartoon">
    <div class="cartoon-body">
        <div class="cartoon-bottom">
            <div class="cartoon-bottombody">
                <div class="bg-block-00">
                    <div class="i-center">
                        <div class="center-block">
                            <i></i>
                        </div>
                    </div>
                    <div class="bg-block">
                        <!-- 5个一组好计数1-5 -->
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <!-- 5个一组好计数6-10 -->
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <!-- 5个一组好计数11-15 -->
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <!-- 5个一组好计数16-20 -->
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <i></i>
                        <!-- 5个一组好计数21-25 -->
                        <i></i>
                        <i></i>
                    </div>
                </div>
                <div class="bg-block-01">
                    <div class="svg-block">
                        <svg width="103" height="101" xmlns="http://www.w3.org/2000/svg">
                            <path d="M103 3C47.772 3 3 47.772 3 103" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="141" height="193" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.82 189.602C80.981 162.966 138 88.142 138 0" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="362" height="90" xmlns="http://www.w3.org/2000/svg">
                            <path d="M360 44.192C240.133-25.013 92.358-2.097-1.342 90.975" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="362" height="90" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 45.808C121.036 114.533 267.594 92.41 361.385.95" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="302" height="493" xmlns="http://www.w3.org/2000/svg">
                            <path d="M300.472 3.63C87.086 60.806-39.548 280.14 17.63 493.528" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="252" height="548" xmlns="http://www.w3.org/2000/svg">
                            <path d="M235.995 547.206C284.45 338.458 193.991 113.988-1.225 1.28" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="306" height="687" xmlns="http://www.w3.org/2000/svg">
                            <path d="M304.049 2.836C60.45 143.478-47.218 429.143 27.139 687.81" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="197" height="574" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.987 571.687C158.583 426.155 225.093 207.343 178.593.806" stroke="#13274D" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="1073" height="1198" viewBox="0 0 1073 1198" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.695 1090.46c269.956 167.294 628.995 133.8 863.28-100.485 45.103-45.103 82.764-94.83 112.984-147.654C1130.9 574.98 1093.239 228.29 864.975.025" stroke="#0A2A66" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="460" height="719" viewBox="0 0 460 719" xmlns="http://www.w3.org/2000/svg">
                            <path d="M570.488-44.687C242.058 54.915 3 360.04 3 721" stroke="#0A2A66" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="310" height="719" viewBox="0 0 310 719" xmlns="http://www.w3.org/2000/svg">
                            <path d="M-422.916 963.101C-1.836 845.263 307 458.698 307 0" stroke="#0A2A66" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                    </div>
                </div>
                <div class="bg-block-02">
                    <div class="svg-block">
                        <svg width="64" height="94">
                            <path d="M63 94C63 52.29 37.465 16.545 1.174 1.545" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7" />
                        </svg>
                        <svg width="131" height="189">
                        <path d="M130.638 1.728C54.895 30.2 1 103.306 1 189" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="90" height="365">
                        <path d="M48.808 364C118.277 243.676 94.92 95.232.958 1.59" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="298" height="374">
                            <path d="M.528 372.37c173.838-46.58 290.1-200.78 296.31-371.71" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="685" height="253">
                            <path d="M.775 184.306c239.147 138.07 544.942 56.133 683.013-183.013" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="822" height="302">
                            <path d="M1.434.451c165.685 286.976 532.64 385.3 819.615 219.615" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="416" height="562">
                            <path d="M1.263.847C69.821 373.034 395.983 655 788 655" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="801" height="801">
                            <path d="M800 801C800 359.172 441.828 1 0 1" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="986" height="453">
                            <path d="M984.432 451.953C781.42 100.327 376.962-62.483 1 25.136" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="956" height="703">
                            <path d="M954.977 702.289C827.729 296.279 448.552 1.71.576 1.71" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="1001" height="1001">
                            <path d="M1 0c0 552.285 447.715 1000 1000 1000" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="1201" height="1201">
                            <path d="M0 1200c662.742 0 1200-537.258 1200-1200" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="383" height="1841">
                            <path d="M1.239 1839.478C508.92 1331.796 508.92 508.682 1.239 1" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="1178" height="963">
                            <path d="M1177.236 1C596.266 1 111.726 413.859 1 962.189" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="536" height="252">
                            <path d="M535.668 11.44C330.376-30.876 112.31 59.82 1.763 251.293" stroke="#003EB3" stroke-width="2" fill="none" fill-rule="evenodd" opacity=".7"/>
                        </svg>
                        <svg width="1086" height="264" viewBox="0 0 1086 264" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 2.58C284.236 279.514 725.84 347.14 1084.27 140.202" stroke="#0A2A66" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                        <svg width="328" height="1368" viewBox="0 0 328 1368" xmlns="http://www.w3.org/2000/svg">
                            <path d="M171.98 2C-95.818 426.987-44.629 995.351 325.545 1365.525" stroke="#0A2A66" stroke-width="6" fill="none" fill-rule="evenodd" opacity=".7" stroke-dasharray="2,6"/>
                        </svg>
                    </div>
                </div>
                <div class="bg-block-03">
                    <div class="svg-block">
                        <svg width="196" height="74" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.113 7.113C18.851 43.966 54.885 70 97 70c44.276 0 81.832-28.775 94.986-68.645" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="195" height="145" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.22 1.204C29.768 82.24 105.522 141 195 141" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="194" height="143" xmlns="http://www.w3.org/2000/svg">
                        <path d="M189.924 141.147C163.574 61.482 88.5 4 0 4" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="95" height="367" xmlns="http://www.w3.org/2000/svg">
                        <path d="M44.192 2C-25.197 122.186-1.974 270.427 91.718 364.087" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="325" height="199" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.11 2.833C72.524 115.49 192.982 187.346 324.102 194.508" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="383" height="303" xmlns="http://www.w3.org/2000/svg">
                        <path d="M378.37 301.472C331.198 125.423 173.65 8.424.13 4.98" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="144" height="501" xmlns="http://www.w3.org/2000/svg">
                        <path d="M28.786 1.872c-56.214 173.818-12.104 364.707 111.86 496.265" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="678" height="307" xmlns="http://www.w3.org/2000/svg">
                        <path d="M674.012 304.686C535.535 64.836 256.465-43.234 1 24.47" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="212" height="858" xmlns="http://www.w3.org/2000/svg">
                        <path d="M101.73 2.919C-57.791 271.534-22.026 623.923 209.026 854.975" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="869" height="213" xmlns="http://www.w3.org/2000/svg">
                        <path d="M866.93 109.555C596.95-57.876 237.794-24.422 3.453 209.92" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="609" height="595" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 590.997c296.683-65.513 531.514-295.73 603.697-589.85" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="253" height="872" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.445 868.711C220.71 638.63 302.08 307.423 213.663 1.57" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="457" height="1236" xmlns="http://www.w3.org/2000/svg">
                        <path d="M455 3.833C24.537 252.361-122.951 802.793 125.577 1233.256" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="330" height="1320" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.752 1316.31C359.812 960.25 420.748 420.822 186.558 2" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="669" height="689" xmlns="http://www.w3.org/2000/svg">
                        <path d="M667.132 4.702C346.422 105.304 95.282 363.11 4 687.834" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    <svg width="961" height="1179" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.443 0c0 578.878 409.892 1062.021 955.272 1175.026" stroke="#071E47" stroke-width="8" fill="none" fill-rule="evenodd" opacity=".7"/>
                    </svg>
                    </div>
                </div>
                <div class="bg-block-04"></div>

            </div>
        </div>
        <div class="cartoon-top" >
            <div class="cartoon-mblock">
                <i class="tsa-logo"></i>
                <div class="tsa-title">腾讯广告品牌资源</div>
                <div class="tsa-content">
                    <div class="tsa-part">
                        <p>欢迎使用腾讯广告品牌资源。</p>
                        <p>在这里你可以了解腾讯广告的品牌形象规范，下载多种格式的品牌设计资源。</p>
                        <p>为了确保品牌形象对内外的可识别性和统一性，请严格遵循规范使用品牌资源。</p>
                    </div>
                    <div class="tsa-res">Tencent Social Ads Brand Resources</div>
                </div>
                <div class="tsa-qcode">
                    <div class="qrblock">
                        <div class="qrbody">
                            <img src="../assets/qrcode.png" alt="">
                        </div>
                    </div>
                </div>
                <div class="tsa-godown"></div>
            </div>
        </div>
    </div>
</transition>
</template>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
    @import "../styles/tsa";
</style>