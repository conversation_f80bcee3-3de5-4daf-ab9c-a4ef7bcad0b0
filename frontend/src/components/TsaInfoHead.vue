<script>
export default {
  name: "TsaInfoHead",

  props: ["infohead"],
  mounted() {
    //console.log(this.info)
  },
};
</script>

<template>
  <div class="info-head">
    <div class="title">{{ infohead.title }}</div>
    <p class="content">{{ infohead.content }}</p>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";
.info-head {
  margin-bottom: 44px;
  color: $text-color;
  .title {
    font-size: 36px;
    font-family: PingFangSC-Medium;
    margin-bottom: 20px;
  }
  .content {
    color: $text-color;
    font-size: 16px;
    letter-spacing: 0.5px;
    line-height: 1.5;
  }

  @media (max-width: $screen-lg) {
    .title {
      font-size: 32px;
    }
    .content {
      font-size: 14px;
    }
  }
}
</style>
