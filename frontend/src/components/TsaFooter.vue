<script>
export default {
  name: "T<PERSON><PERSON>ooter",
};
</script>

<template>
  <div class="footer">
    <p>腾讯广告 品牌公关中心&设计中心</p>
    <p>
      Copyright © 1998 - {{ new Date().getFullYear() }} Tencent Inc. All Rights
      Reserved.
    </p>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/headerlink";
.footer {
  margin-top: 64px;
  margin-bottom: 96px;
  font-size: 0;
  text-align: center;
  color: $sub-text-color;
  p {
    font-size: 14px;
    margin-bottom: 40px;
    &:first-child {
      margin-bottom: 16px;
    }
  }
  .footer-bgc {
    height: 8px;
    width: 100%;
  }
  @media (max-width: $screen-md) {
    p {
      font-size: 12px;
      margin-bottom: 15px;
    }
  }
}
</style>
