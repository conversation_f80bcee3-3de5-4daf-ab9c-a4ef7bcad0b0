<script>
export default {
  name: "TsaSmallCard",
  props: {
    pic: {},
    options: {},
    ppt: {},
  },
  mounted() {
    console.log(this.ppt);
  },
};
</script>

<template>
  <div class="small-card">
    <div class="options">
      <div class="options-pic">
        <img :src="pic" alt="" />
      </div>
      <div class="options-title">{{ options.title }}</div>
    </div>
    <div class="button">
      <a class="button-a" :href="options.button.link" target="_blank">
        {{ options.button.value }}
      </a>
    </div>
    <div class="down-ppt" v-if="ppt">
      <a class="button-a" :href="ppt.link" target="_blank">
        <i class="ppt-svg"></i>
        {{ ppt.value }}
      </a>
    </div>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;

.small-card {
  position: relative;
  width: 100%;
  height: 368px;
  padding: 56px 40px;
  font-size: 0;
  overflow: hidden;

  @media screen and (max-width: $screen-lg) {
    padding: 48px 32px;
    height: 304px;
  }
  .options {
    margin-bottom: 104px;

    @media screen and (max-width: $screen-lg) {
      margin-bottom: 86px;
    }

    .options-pic {
      width: 150px;
      margin-bottom: 36px;
      @media screen and (max-width: $screen-lg) {
        width: 130px;
        margin-bottom: 30px;
      }
      img {
        width: 100%;
      }
    }
    .options-title {
      display: inline-block;
      width: 300px;
      font-size: 28px;
      line-height: 44px;
      color: #0b1531;
      letter-spacing: 0.75px;
      font-weight: bold;
      @media screen and (max-width: $screen-lg) {
        line-height: 36px;
        width: 255px;
        font-size: 22px;
      }
    }
  }
  .button {
    position: absolute;
    left: 32px;
    bottom: 56px;
    width: 144px;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    box-sizing: border-box;
    border-radius: 29px;
    background-color: #296bef;
    box-shadow: 0 1px 0 rgba(44, 114, 255, 0.08),
      0 6px 12px rgba(44, 114, 255, 0.18);
    cursor: pointer;
    color: #fff;
    font-size: 16px;
    padding: 0 32px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    text-decoration: none;
    outline: 0;
    transition: all 0.4s ease;
    @media screen and (max-width: $screen-lg) {
      bottom: 48px;
      width: 112px;
      height: 40px;
      line-height: 40px;
      padding: 0 22px;
      font-size: 14px;
    }
    &:hover {
      color: #fff;
      background-color: #165be0;
      box-shadow: none;
    }
    .button-a {
      width: 100%;
      height: 100%;
      font-size: 16px;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      @media screen and (max-width: $screen-lg) {
        font-size: 14px;
      }
    }
  }

  .down-ppt {
    position: absolute;
    right: 32px;
    bottom: 56px;
    width: 144px;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    box-sizing: border-box;
    border: 2px solid #296bef;
    border-radius: 29px;
    background-color: #fff;
    box-shadow: 0 1px 0 rgba(44, 114, 255, 0.08),
      0 6px 12px rgba(44, 114, 255, 0.18);
    cursor: pointer;
    color: #fff;
    font-size: 16px;
    padding: 0 10px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    text-decoration: none;
    outline: 0;
    transition: all 0.4s ease;
    @media screen and (max-width: $screen-lg) {
      bottom: 48px;
      width: 112px;
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      font-size: 14px;
    }

    .button-a {
      width: 100%;
      height: 100%;
      font-size: 16px;
      color: #296bef;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.4s ease;
      @media screen and (max-width: $screen-lg) {
        font-size: 14px;
      }
    }

    .ppt-svg {
      margin-right: 6px;
      display: inline-block;
      width: 16px;
      height: 16px;
      background-position: center;
      background-image: url("../assert/ICON/ppt_normal.svg");
      transition: all 0.4s ease;

      @media screen and (max-width: $screen-lg) {
        margin-right: 4px;
        width: 14px;
        height: 14px;
      }
    }
    &:hover {
      color: #fff;
      background-color: #165be0;
      box-shadow: none;

      .button-a {
        color: #fff;
      }

      .ppt-svg {
        background-image: url("../assert/ICON/ppt_hover.svg");
      }
    }
  }
}
</style>
