<script>
export default {
  name: "TsaDown",

  props: {
    image: {},
    href: {
      default: "/",
    },
    name: {},
    desc: {
      default: () => [],
      type: Array,
    },
  },
  mounted() {
    // console.log(this.name)
  },
};
</script>

<template>
  <div>
    <a class="down-part" :href="href">
      <div class="down-img">
        <img :src="image" alt="" />
      </div>
      <div class="down-button">
        <i class="down-icon"></i>
        <div class="down-name">{{ name }}</div>
      </div>
    </a>
    <div class="down-desc" v-if="desc.length !== 0">
      <div class="desc-item" v-for="(item, key) in desc" :key="key">
        {{ item }}
      </div>
    </div>
  </div>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/tsa";

.down-desc {
  color: $sub-text-color;
  padding-top: 20px;
  letter-spacing: 0.5px;
  line-height: 1.5;
}
.down-part {
  width: 340px;
  height: 178px;
  border: 2px solid rgba(0, 116, 255, 0.1);
  border-radius: 12px;
  box-sizing: content-box;
  text-align: center;
  display: block;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba($color: #0074ff, $alpha: 0.05);
  @media screen and (max-width: $screen-lg) {
    width: 308px;
    height: 164px;
    font-size: 14px;
  }
  .down-img {
    width: 100%;
    width: 340px;
    height: 136px;
    @media screen and (max-width: $screen-lg) {
      width: 308px;
      height: 124px;
    }
    img {
      width: 100%;
    }
  }

  .down-button {
    height: 42px;
    font-size: 16px;
    box-sizing: border-box;
    border-top: 2px solid rgba(0, 116, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
    @media screen and (max-width: $screen-lg) {
      font-size: 14px;
    }
    .down-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      transition: all 0.8s;
      background-image: url("../assert/ICON/icon_down.svg");
      background-position: center;
      z-index: 1;
      fill: $desc-text-color;
    }
    svg {
      width: 16px;
      height: 16px;
      fill: $desc-text-color;
      font-weight: bold;
    }
    .down-name {
      color: $desc-text-color;
      padding-left: 14px;
    }
  }
  &:hover {
    border: 2px solid $primary;
    box-shadow: 0 4px 20px rgba($color: #0074ff, $alpha: 0.15);
    .down-button {
      background-color: $primary;
      border-top: 2px solid $primary;
      .down-icon {
        background-image: url("../assert/ICON/icon_down_hover.svg");
      }
      .down-name {
        color: #fff;
        font-family: PingFangSC-Medium;
      }
      svg {
        fill: #fff;
      }
    }
  }

  // @media (max-width: $screen-md) {
  // 		margin-bottom: 0px;
  // }

  // a {
  // 		display: flex;
  // 		justify-content: center;
  // 		align-items: center;
  // 		height: calc(100% - 42px);
  // 		border-bottom: 8px solid rgba(0, 116, 255, 0.1);
  // 		overflow: hidden;
  // }
  // .img-border {
  // 		width: 100%;
  // 		border-bottom: 1px solid #ededed;
  // 		text-align: center;
  // }
  // .down-img {
  // 		display: inline-block;
  // 		overflow: hidden;
  // 		img {
  // 				width: 100%;
  // 				max-width: 100%;
  // 		}
  // 		@media (max-width: 1150px) {
  // 				width: 328px;
  // 		}
  // 		@media (max-width: 840px) {
  // 				width: 100%;
  // 		}
  // }
  // .down-button {
  // 		position: absolute;
  // 		margin: 0 auto;
  // 		bottom: 0;
  // 		left: 0;
  // 		right: 0;
  // 		height: 48px;
  // 		// border-radius: 0 0 12px 12px;
  // 		display: flex;
  // 		justify-content: center;
  // 		align-items: center;
  // 		color: #0091E6;
  // 		background: linear-gradient(to left, #0099FF, #008DE8);
  // 		transition: all .8s;
  // 		.down-icon {
  // 				display: inline-block;
  // 				width: 16px;
  // 				height: 16px;
  // 				transition: all .8s;
  // 				background-image: url('../assets/down.svg');
  // 				z-index: 1;
  // 		}
  // 		svg {
  // 				width: 16px;
  // 				height: 16px;
  // 				fill: #0091E6;
  // 				font-weight: bold;
  // 		}
  // 		.down-bgc {
  // 				position: absolute;
  // 				top: 0;
  // 				left: 0;
  // 				display: inline-block;
  // 				height: 100%;
  // 				width: 100%;
  // 				background: #fff;
  // 				// border-radius: 0 0 12px 12px;
  // 				opacity: 1;
  // 				transition: opacity .8s;
  // 				z-index: 0;
  // 		}
  // 		.down-name {
  // 				margin-left: 16px;
  // 				letter-spacing: 1px;
  // 				font-size: 14px;
  // 				z-index: 1;
  // 		}
  // }
  // &:hover {
  // 		// box-shadow: 0px 12px 40px 0px rgba(0, 0, 0, 0.08);
  // 		.down-button {
  // 				color: #fff;
  // 				transition: all .8s;
  // 				.down-icon {
  // 						background-image: url('../assets/down_white.svg');
  // 				}
  // 				.down-bgc {
  // 						opacity: 0;
  // 				}
  // 		}
  // }
}
</style>
