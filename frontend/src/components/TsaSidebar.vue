<script>
export default {
  name: "TsaSidebar",
  props: {
    options: {},
    className: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  // computed: {
  //   change: {
  //     get() {
  //       const route = this.$route
  //       const { meta } = route
  //       this.activeIndex = meta.index
  //       return this.activeIndex
  //     },
  //     set(value) {
  //       console.log(this.activeIndex)
  //       this.activeIndex = value
  //     }
  //   }
  // },
  mounted() {
    const route = this.$route;
    const { meta } = route;
    this.activeIndex = meta.index;
  },
  watch: {
    $route() {
      const route = this.$route;
      const { meta } = route;
      this.activeIndex = meta.index;
    },
  },
  methods: {
    changeRoute(option) {
      this.activeIndex = option.index;
      this.$router.push(option.link);
      this.$emit("change");
    },
  },
};
</script>

<template>
  <ul class="sidebar" :class="{ fixed: className }">
    <li
      class="sidebar-item"
      v-for="(option, key) in options"
      :key="key"
      v-bind:class="{ 'sidebar-active': activeIndex === option.index }"
    >
      <span class="sidebar-a" @click="changeRoute(option)">{{
        option.name
      }}</span>
      <!--      <router-link class="sidebar-a" :to="option.link">{{ option.name }}</router-link>-->
    </li>
  </ul>
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
@use "../styles/default" as *;
@import "../styles/headerlink";
.sidebar {
  box-sizing: border-box;
  width: 244px;
  height: 600px;
  position: sticky;
  top: 75px;
  font-size: 16px;
  padding-top: 48px;
  @media screen and (max-width: $screen-lg) {
    width: 200px;
    font-size: 14px;
    padding-top: 40px;
  }
}
.sidebar-item {
  display: block;
  padding-left: 52px;
  height: 48px;
  line-height: 48px;
  cursor: pointer;
  @media screen and (max-width: $screen-lg) {
    padding-left: 44px;
    height: 40px;
    line-height: 40px;
  }
  .sidebar-a {
    display: block;
    width: 100%;
    height: 100%;
    color: $desc-text-color;
    // font-family: PingFangHK-Medium;
    font-family: PingFangSC-Regular;
    &:hover {
      color: $primary;
      // font-family: PingFangSC-Medium;
    }
  }
}
.sidebar-active {
  background-color: $body-bg-color;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    /*z-index: 99;*/
    right: -3px;
    top: 0;
    width: 4px;
    height: 48px;
    border-radius: 12px;
    background-color: $primary;

    @media screen and (max-width: $screen-lg) {
      height: 40px;
    }
  }
  .sidebar-a {
    color: $primary;
    font-family: PingFangSC-Medium;
  }
}
.fixed {
  position: fixed;
  top: 76px;
  @media screen and (max-width: $screen-lg) {
    top: 76px;
  }
}
</style>
