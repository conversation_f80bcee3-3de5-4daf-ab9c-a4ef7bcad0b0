<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="Web App" />
    <meta name="format-detection" content="telephone=no" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <link rel=icon href='./favicon.ico' type=image/x-icon>
    <title>腾讯广告品牌资源</title>
    <!--      <script>-->
    <!--          function handleResponse (resp) {-->
    <!--              if(!resp || !resp.data) {-->
    <!--                  errorCb()-->
    <!--                  console.log('err')-->
    <!--              }-->
    <!--              else {-->
    <!--                  localStorage.removeItem('redirect_to_login')-->
    <!--                  successCb(resp.data)-->
    <!--              }-->
    <!--          }-->
    <!--      </script>-->
    <!--      <script src="http://logic.ad.mmbiz.oa.com/cgi-bin/privilege/privilegemgr?action=whoami&callback=handleResponse"></script>-->
  <script type="module" src="/src/main.js"></script>
  </head>
  <body style="font-size: 0.14rem">
    <noscript>
      <strong
        >We're sorry but 腾讯广告品牌资源 doesn't work properly without
        JavaScript enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
  <script>
    (function (doc, win) {
      var docEl = doc.documentElement;
      var resizeEvt =
        "orientationchange" in window ? "orientationchange" : "resize";
      var recalc = function () {
        var clientWidth = docEl.clientWidth;
        if (!clientWidth) return;
        //     加 130px 的限制
        docEl.style.fontSize =
          (100 * (clientWidth / 375) > 100 ? 100 : 100 * (clientWidth / 375)) +
          "px";
      };

      if (!doc.addEventListener) return;
      win.addEventListener(resizeEvt, recalc, false);
      doc.addEventListener("DOMContentLoaded", recalc, false);
    })(document, window);
  </script>
  <script>
    var _mtac = {};
    (function () {
      var mta = document.createElement("script");
      mta.src = "//pingjs.qq.com/h5/stats.js?v2.0.4";
      mta.setAttribute("name", "MTAH5");
      mta.setAttribute("sid", "500709514");
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(mta, s);
    })();
  </script>
  <!-- 使用CDN的JS文件 -->
  <!-- <% for (var i in htmlWebpackPlugin.options.cdn &&
  htmlWebpackPlugin.options.cdn.js) { %> -->
  <!-- <script
          type="text/javascript"
          src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"
  ></script> -->
  <!-- <% } %> -->
</html>
