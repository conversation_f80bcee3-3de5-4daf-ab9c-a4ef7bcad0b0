import express from 'express'
import history from 'connect-history-api-fallback'
import bodyParser from 'body-parser'
import chalk from 'chalk'
import getIPAddress from "./utils/getIp"
import fs from 'fs'
import path from 'path'
import multer from 'multer'
import { app, http } from "./utils/socket"
import crypto from "crypto";

app.set('port', process.env.PORT || 8082)
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.all('*', (req, res, next) => {
    const { origin, Origin, referer, Referer } = req.headers
    const allowOrigin = origin || Origin || referer || Referer || '*'
    res.header("Access-Control-Allow-Origin", allowOrigin)
    res.header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
    res.header("Access-Control-Allow-Methods", "PUT,POST,GET,DELETE,OPTIONS")
    res.header("Access-Control-Allow-Credentials", true)
    res.header("X-Powered-By", 'Express')
    if (req.method === 'OPTIONS') {
        res.sendStatus(200)
    } else {
        next()
    }
})


app.use(history())
app.use(express.static('./dist'))
// development only

var publicDir = 'public/';

var tmpDir = publicDir + 'tmp/';
if (!fs.existsSync(tmpDir)) {
    fs.mkdirSync(tmpDir);
}

global.appNs = {};
var appNs = global.appNs;
// 默认是开启的
appNs.canMakeSignature = true;

var uploadDestDir = path.resolve('./uploads/')
var upload = multer({ dest: uploadDestDir })

const SmartProxy = require("@tencent/smart-proxy");
SmartProxy.setOptions({ token: 'GlnAH1OJ7E8oIPuuXSJvd6UUaDgfmPGH' });
app.use(SmartProxy.auth());

app.all('*', function (req, res, next) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'XMLHttpRequest, X-Requested-With');
    // res.setHeader("Content-Type", "application/json;charset=utf-8");
    next()
})

// app.post('/image', upload.single('file'), require('./routes/image').index);
app.post('/make', require('./routes/index').index);

function sha256(string) {
    const hash = crypto.createHash("sha256");
    hash.update(string);
    return hash.digest("hex"); // 返回16进制hash码
}
app.post('/auth', ((req, res, next) => {
    const timestamp = req.header("timestamp");
    const signature = req.header("signature");
    const staffId = req.header("staffid");
    const staffName = req.header("staffname");
    const xRioSeq = req.header("x-rio-seq");
    const xExtData = req.header("x-ext-data") || ""; // 办公网访问，这里要设置成空字符串
    const token = "GlnAH1OJ7E8oIPuuXSJvd6UUaDgfmPGH";
    const nowTimestamp = (new Date().getTime() / 1000).toFixed(0);
    // 校验时间戳,误差不得超过180秒
    if (Math.abs(Number(nowTimestamp) - timestamp) > 180) {
        console.log(
            `smart-proxy timestamp check fail, nowTimestamp: ${nowTimestamp}, timestamp: ${timestamp}`
        );
        // res.status(403).send("smart-proxy timestamp check fail");
        return;
    }

    // 签名为大写英文
    const computedSignature = sha256(
        `${timestamp}${token}${xRioSeq},${staffId},${staffName},${xExtData}${timestamp}`
    ).toUpperCase();

    // 校验签名
    if (computedSignature !== signature) {
        console.log(
            `smart-proxy signature check fail, before signature string: ${timestamp}${token}${xRioSeq},${staffId},${staffName},${xExtData}${timestamp}`
        );
        // res.status(403).send("smart-proxy signature check fail");

        return;
    }

    // 合法用户正常放行
    next();
    return {
        rtx: staffName,
    };
}));
http.listen(app.get('port'), () => {
    console.log(chalk.bgGreen(`Server running`) + (` at https://${getIPAddress()}:${app.get('port')}`))
})
