
var fs = require('fs')
var utils = require('../lib/utils')
var	gmUtils = require('../lib/gm')

var appNs = global.appNs;

function removeBase64Prefix(dataurl) {
  return dataurl.replace(/^data:image\/png;base64,/, "")
}

exports.index = function(req, res) {
  console.log('req.body', req.body)
  console.log('req.files', req.file)
  setTimeout(function () {
    fs.unlink(req.file.path)    
  }, 1000)
  res.status(204).end()
}