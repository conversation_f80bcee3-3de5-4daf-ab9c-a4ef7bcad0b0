
var fs = require('fs')
var utils = require('../lib/utils')
var	gmUtils = require('../lib/gm2')

var appNs = global.appNs;

function removeBase64Prefix(dataurl) {
  return dataurl.replace(/^data:image\/png;base64,/, "")
}

exports.index = function(req, res) {
  if(!appNs.canMakeSignature)
    return res.json({
      err: -2,
      msg: '状态错误'
    })

  var occupation = req.body['occupation']
  var tel = req.body['tel']
  var mobile = req.body['mobile']
  var rtxName = req.body['rtxName']
  var location = req.body['location']
  var team = req.body['team']
  var qrcodeData = removeBase64Prefix(req.body['qrcodeData'])
  var avatarData = removeBase64Prefix(req.body['avatarData'])

  var qrcodeData = Buffer.from(qrcodeData, "base64");
  var avatarData = Buffer.from(avatarData, "base64");

  gmUtils.maker({
    rtxName: rtxName,
    occupation: occupation,
    tel: tel,
    mobile: mobile,
    team: team,
    location: location,
    locationString: req.body['locationString'],
    depart: req.body['depart'],
    qrcodeData: qrcodeData,
    avatarData: avatarData
  }, function (err, imageData) {

    if(err) {
      return res.json({
        err: 1,
        msg: '错误'
      })
    }
    res.json({
      err: 0,
      imageData: imageData.toString('base64')
    });

  })
}
