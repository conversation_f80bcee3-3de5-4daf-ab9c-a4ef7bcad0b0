# 资源站点

## 目录说明

#### project

资源站点 web 端代码

#### utils

node 端方法

#### public

node 端处理上传图片所需要的资源

### ecosystem.config.js

node 端运行时的环境变量

## 安装
```
  npm install or yarn i
```

## 本地调试
```
  node index.js
```
开启本地服务

## 部署
将 brand 目录下的文件上传到服务器上（node_modules 以及其他依赖可不传），服务器需安装 ImageMagick 依赖

运行
```
 yum -y install ImageMagick
```
然后安装 node 服务所需要的依赖
```
 npm i
```
如果需要服务常驻，还需要安装 pm2
```
npm install pm2 -g
```
安装后运行 
```
pm2 start pro
```
即可部署成功

## 注意事项

1. 本地调试需同时开启本地服务和运行web端服务

2. 如不安装 pm2 ，直接运行
```
node index.js
```
但此时会缺少全局变量的端口号，缺少时会默认为本地调试端口号：8081,如果不使用 pm2，务必记得修改默认端口号保持与服务器一致