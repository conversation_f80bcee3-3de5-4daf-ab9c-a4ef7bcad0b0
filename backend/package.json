{"name": "material_qrcode_node", "version": "0.0.1", "description": "", "main": "index.js", "scripts": {"start": "cross-env NODE_ENV=development nodemon --harmony index.js", "check": "cross-env NODE_ENV=production nodemon --harmony index.js", "pro": "pm2 start ecosystem.config.js --only pro", "test": "pm2 start ecosystem.config.js --only test"}, "repository": {"type": "git", "url": "http://git.code.oa.com/adamyu/material_qrcode_node.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@tencent/smart-proxy": "^1.3.2", "async": "^3.2.0", "babel": "^6.23.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-export-extensions": "^6.22.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "babel-register": "^6.26.0", "body-parser": "^1.19.0", "chalk": "^4.0.0", "connect-history-api-fallback": "^1.6.0", "cross-env": "^7.0.2", "express": "^4.17.1", "formidable": "^1.2.2", "gm": "^1.23.1", "multer": "^1.4.2", "node-uuid": "^1.4.8", "nodemon": "^2.0.2"}}