
var fs = require('fs')
var gm = require('gm')
var utils = require('../lib/utils')
var imageMagick = gm.subClass({ imageMagick : true });
var async = require('async');
var publicDir = 'public/';
var tmpDir = publicDir + 'tmp/';

var spaTpls = {
  binhai: publicDir + 'signature/tpl.png',
}
var wxTpls = {
  binhai: publicDir + 'signature/tpl/wx/binhai.png',
}
var omTpls = {
  binhai: publicDir + 'signature/tpl/om/binhai.png',
  gz: publicDir + 'signature/tpl/om/gz.png',
  bj: publicDir + 'signature/tpl/om/bj.png'
}
var line = publicDir + 'signature/line.png'
var logo = publicDir + 'signature/logo.png'
var lomoDistPath = tmpDir + 'test.png'

function create(data, options) {
  // 边框 shadow 偏移
  var marginLeft = 4
  imageMagick(options.tpl)
    .fontSize(16)
    // .out('-kerning', 1)
    .fill("#212121")
    //.font("./fonts/SourceHanSansCN-Medium.otf")
    .font("./fonts/PingFangBold.otf")
    // North South SouthWest SouthEast NorthWest
    .drawText(114, 84, data.rtxName, 'NorthWest')
    .fontSize(14)
    .font("./fonts/PingFang.ttc")
    .drawText(114, 107, data.occupation, 'NorthWest')
    .fontSize(12)
    .fill("#757575")
    .font("./fonts/PingFang.ttc")

    .drawText(26, 30, data.depart, 'NorthEast')
    .font("./fonts/SF-Pro-Text-Regular.otf")
    .fontSize(12)
    .drawText(372 + marginLeft, 77, data.tel, 'NorthWest')
    .font("./fonts/SF-Pro-Text-Regular.otf")
    .fontSize(12)
    .drawText(372 + marginLeft, 97, data.mobile, 'NorthWest')
    .font("./fonts/PingFang.ttc")
    .fontSize(12)
    .drawText(372 + marginLeft, 117, data.locationString, 'NorthWest')
    .setFormat('png')
    .toBuffer(function (err, buffer) {
      if (err) return console.log(err);

      // 优化方向，使用 layer images
      // gm()
      //   .in('-page', '+0+0')
      //   .in(publicDir + 'signature/avatar.png')
      //   .in('-page', '+0+0')
      //   .in(lomoDistPath)
      //   .write(publicDir + 'finalimg.png', function(err) {
      //     if (err) {
      //       console.error(err);
      //       return;
      //     }
      //     require('child_process').exec('open ' + publicDir + 'finalimg.png')
      //   });

      // return cb(null, buffer);

      // composite
      // 人头位置
      imageMagick(buffer)
        .geometry('+28+70')
        .composite(data.avatarImg)
          .setFormat('png')
        .toBuffer(function (err, buffer) {
          if (err) return console.log(err);

          // 二维码位置
          imageMagick(buffer)
            // .geometry('+' + (320 + marginLeft) + '+' + (33 + marginLeft))
            .geometry('+' + (277 + marginLeft) + '+' + (65 + marginLeft))
            .composite(data.qrcodeImg)
              .setFormat('png')
            .toBuffer(function (err, buffer) {
              if (err) return console.log(err);

              imageMagick(buffer)
                .geometry('+' + (308 + marginLeft) + '+' + (95 + marginLeft))
                .composite(logo)
                  .setFormat('png')
                .toBuffer(function (err, buffer) {
                  if (err)
                    return console.log(err);
                  fs.unlink(data.avatarImg, function(){})
                  fs.unlink(data.qrcodeImg, function(){})
                  options.cb(null, buffer);
                })
            });

        })

    });
}

exports.maker = function(data, cb) {
  var timeStampFileName = +new Date() + '.png';
  var qrcodeImg = tmpDir + 'qrcode' + timeStampFileName;
  var avatarImg = tmpDir + 'avatar' + timeStampFileName;
  var tpl = spaTpls.binhai

  var options = {
    cb: cb,
    tpl: tpl
  }

  async.parallel([
      function(callback){
        // imageMagick(data.qrcodeData)
          // resize 导致变形模糊
          // .resize(78, 78)
          // .write(qrcodeImg, function(err) {
          //   if (err) {
          //     console.error(err);
          //     return;
          //   }
          //   data.qrcodeImg = qrcodeImg;
          //   callback()
          // })
          fs.writeFile(qrcodeImg, data.qrcodeData, function(err) {
            if (err) {
              console.error(err);
              return;
            }
            data.qrcodeImg = qrcodeImg;
            callback()
          });
      },
      function(callback){
        fs.writeFile(avatarImg, data.avatarData, function(err) {
          if (err) {
            console.error(err);
            return;
          }
          data.avatarImg = avatarImg;
          callback()
        });
      }
  ], function () {
      create(data, options)
  });

}
