
var fs = require('fs')
var gm = require('gm')
var utils = require('../lib/utils')
var imageMagick = gm.subClass({ imageMagick : true });
var async = require('async');
var publicDir = 'public/';
var tmpDir = publicDir + 'tmp/';
var tpl = publicDir + 'signature/tpl.png'
var logo = publicDir + 'signature/logo.png'
var lomoDistPath = tmpDir + 'test.png'

function create(data, cb) {
  // console.log(data)

  imageMagick(tpl)
    .fontSize(20)
    .fill("#000")
    .font("./fonts/yahei.ttf")
    // North South SouthWest SouthEast NorthWest
    .drawText(152, 48, data.rtxName, 'NorthWest')
    .fontSize(14)
    .fill("#aaaaaa")
    .drawText(490, 49 , data.tel, 'NorthWest')
    .drawText(490, 72, data.mobile, 'NorthWest')
    .fill("#000")
    .out('-kerning', 1)
    .drawText(152, 75, data.occupation, 'NorthWest')
    .setFormat('PNG')
    .toBuffer(function (err, buffer) {
      if (err) {
        console.log(err);
      }

      // 优化方向，使用 layer images
      // gm()
      //   .in('-page', '+0+0')
      //   .in(publicDir + 'signature/avatar.png')
      //   .in('-page', '+0+0')
      //   .in(lomoDistPath)
      //   .write(publicDir + 'finalimg.png', function(err) {
      //     if (err) {
      //       console.error(err);
      //       return;
      //     }
      //     require('child_process').exec('open ' + publicDir + 'finalimg.png')
      //   });

      // return cb(null, buffer);

      // composite
      imageMagick(buffer)
        .geometry('+38+38')
        .composite(data.avatarImg)
        .setFormat('PNG')
        .toBuffer(function (err, buffer) {
          if (err) return console.log(err);
          imageMagick(buffer)
            .geometry('+396+48')
            .composite(data.qrcodeImg)
            .setFormat('PNG')
            .toBuffer(function (err, buffer) {
              if (err) return console.log(err);
              imageMagick(buffer)
                .geometry('+425+76')
                .composite(logo)
                .setFormat('PNG')
                .toBuffer(function (err, buffer) {
                  if (err)
                    return console.log(err);
                  fs.unlink(data.avatarImg)
                  fs.unlink(data.qrcodeImg)
                  cb(null, buffer);
                })
            });
        })

    });
}

exports.maker = function(data, cb) {
  var timeStampFileName = +new Date() + '.png';
  var qrcodeImg = tmpDir + 'qrcode' + timeStampFileName;
  var avatarImg = tmpDir + 'avatar' + timeStampFileName;

  async.parallel([
      function(callback){
        imageMagick(data.qrcodeData)
          .resize(70, 70)
          .write(qrcodeImg, function(err) {
            if (err) return console.log(err);
            data.qrcodeImg = qrcodeImg;
            callback()
          })
      },
      function(callback){
        fs.writeFile(avatarImg, data.avatarData, function(err) {
          if (err) {
            console.error(err);
            return;
          }
          data.avatarImg = avatarImg;
          callback()
        });
      }
  ], function () {
    // console.log('done')
    create(data, cb)
  });

}
