FROM mirrors.tencent.com/ams_public/tlinux2.2-ams-nodejs:12.18.2
# FROM mirrors.tencent.com/ams_tad/amsbrand:latest

RUN ln -s /usr/local/node/bin/node /usr/local/bin/ \
&& ln -s /usr/local/node/bin/npm /usr/local/bin/ \
&& ln -s /usr/local/node/bin/pm2 /usr/local/bin/ \
&& ln -s /usr/local/node/bin/pm2-docker /usr/local/bin/ \
&& ln -s /usr/local/node/bin/npx /usr/local/bin/ \
&& ln -s /usr/local/node/bin/tnpm /usr/local/bin/

ADD ./backend /usr/local/app/

WORKDIR /usr/local/app/

RUN cd /usr/local/app/ && ls

RUN tnpm install -g tnpm

RUN tnpm install

CMD npm run pro

ENTRYPOINT ["npm", "run", "pro"]